"""Redis配置

定义Redis连接和操作的配置。
"""

from dataclasses import dataclass
from .settings import settings


@dataclass
class RedisConfig:
    """Redis配置类 - 任务数据库配置"""

    host: str = settings.redis_host
    port: int = settings.redis_port
    password: str = settings.redis_password
    db: int = settings.redis_db  # 任务数据库（默认0号）
    max_connections: int = settings.redis_max_connections

    # Redis键前缀
    build_task_prefix: str = "build_task"
    build_tasks_set: str = "build_tasks"
    test_task_prefix: str = "test_task"
    test_tasks_set: str = "test_tasks"

    # 过期时间配置（秒）
    task_expire_time: int = 86400 * 7  # 7天

    @property
    def connection_url(self) -> str:
        """获取任务数据库Redis连接URL"""
        return f"redis://:{self.password}@{self.host}:{self.port}/{self.db}"


@dataclass
class CredentialRedisConfig:
    """Redis配置类 - 凭据数据库配置"""

    host: str = settings.redis_host
    port: int = settings.redis_port
    password: str = settings.redis_password
    db: int = settings.credential_redis_db  # 凭据数据库（1号）
    max_connections: int = settings.redis_max_connections

    # 过期时间配置（秒）
    credential_expire_time: int = settings.credential_expire_time

    @property
    def connection_url(self) -> str:
        """获取凭据数据库Redis连接URL"""
        return f"redis://:{self.password}@{self.host}:{self.port}/{self.db}"
    
    def get_build_task_key(self, task_id: str) -> str:
        """获取构建任务的Redis键"""
        return f"{self.build_task_prefix}:{task_id}"
    
    def get_test_task_key(self, task_id: str) -> str:
        """获取测试任务的Redis键"""
        return f"{self.test_task_prefix}:{task_id}"


# 全局Redis配置实例
redis_config = RedisConfig()  # 任务数据库配置
credential_redis_config = CredentialRedisConfig()  # 凭据数据库配置