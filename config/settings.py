"""应用程序配置

定义应用程序的各种配置参数。
"""

from pydantic_settings import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    """应用程序设置"""
    
    # 应用基本配置
    app_name: str = "GWHW MCP Server"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # 服务器配置
    host: str = "0.0.0.0"
    port: int = 8000
    
    # Redis配置
    redis_host: str = "**************"
    redis_port: int = 6379
    redis_password: str = "qzkj"
    redis_db: int = 0
    redis_max_connections: int = 10
    
    # SSH配置
    default_ssh_user: str = "root"
    default_ssh_password: str = "root@123"
    default_remote_ip: str = "*************"
    default_container_name: str = "build"
    ssh_timeout: int = 30
    ssh_keepalive_interval: int = 60
    ssh_keepalive_count_max: int = 3
    
    # 构建配置
    default_build_script_path: str = "/home/<USER>/build"
    default_make_jobs: int = 4
    
    # 插件路径配置
    default_container_plugin_path: str = "/home/<USER>/src/hw/gw_parser/parser/"
    default_server_plugin_path: str = "/opt/apigw/gwhw/parser/"
    
    # 测试配置
    default_wait_seconds: int = 5
    default_log_lines: int = 100
    default_eth_name: str = "lo"  # 默认网口名称
    pcap_task_dir: str = "/opt/pcap/task"
    pcap_replay_dir: str = "/tmp"  # pcap回放测试目录
    upload_log_file: str = "/opt/upload/log.file"
    hw_log_path: str = "/opt/apigw/gwhw/logs/hw.log"
    hw_err_path: str = "/opt/apigw/gwhw/logs/hw.err"
    
    # 日志配置
    log_level: str = "INFO"
    log_file: str = "logs/gwhw_mcp.log"
    log_err: str = "logs/gwhw_mcp.err"
    
    # 任务配置
    task_cleanup_interval: int = 3600  # 1小时
    max_task_history: int = 1000
    auto_delete_completed_tasks: bool = True  # 获取完成任务结果后自动删除

    # 凭据管理配置
    credential_encryption_key: str = "your-32-byte-encryption-key-here"  # AES-256加密密钥
    credential_redis_prefix: str = "server_credential"  # Redis键前缀
    credential_expire_time: int = 86400 * 365  # 凭据过期时间（1年）
    enable_credential_audit: bool = True  # 是否启用凭据操作审计
    credential_backup_enabled: bool = True  # 是否启用凭据备份

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# 全局配置实例
settings = Settings() 