"""Redis客户端工具

提供Redis连接和操作的封装。
"""

import json
import redis.asyncio as redis
from typing import Optional, Any
from core.logging import get_logger
from core.exceptions import RedisConnectionException
from config.redis_config import redis_config

logger = get_logger(__name__)


class RedisClient:
    """Redis客户端封装类"""
    
    def __init__(self):
        self._pool: Optional[redis.ConnectionPool] = None
        self._client: Optional[redis.Redis] = None
    
    async def connect(self) -> None:
        """建立Redis连接"""
        try:
            self._pool = redis.ConnectionPool(
                host=redis_config.host,
                port=redis_config.port,
                password=redis_config.password,
                db=redis_config.db,
                max_connections=redis_config.max_connections,
                decode_responses=True
            )
            self._client = redis.Redis(connection_pool=self._pool)
            
            # 测试连接
            await self._client.ping()
            logger.info(f"Redis连接成功: {redis_config.host}:{redis_config.port}")
            
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            raise RedisConnectionException(str(e))
    
    async def disconnect(self) -> None:
        """断开Redis连接"""
        if self._client:
            await self._client.close()
        if self._pool:
            await self._pool.disconnect()
        logger.info("Redis连接已断开")
    
    async def ping(self) -> bool:
        """测试Redis连接
        
        Returns:
            连接是否正常
        """
        try:
            if not self._client:
                await self.connect()
            
            await self._client.ping()
            return True
            
        except Exception as e:
            logger.error(f"Redis ping失败: {e}")
            return False
    
    async def set_task(self, key: str, task_data: dict[str, Any], expire: Optional[int] = None) -> bool:
        """存储任务数据
        
        Args:
            key: Redis键
            task_data: 任务数据
            expire: 过期时间（秒）
            
        Returns:
            是否成功
        """
        try:
            if not self._client:
                await self.connect()
            
            json_data = json.dumps(task_data, default=str, ensure_ascii=False)
            result = await self._client.set(key, json_data)
            
            if expire:
                await self._client.expire(key, expire)
            
            return bool(result)
            
        except Exception as e:
            logger.error(f"存储任务数据失败 {key}: {e}")
            return False
    
    async def get_task(self, key: str) -> Optional[dict[str, Any]]:
        """获取任务数据
        
        Args:
            key: Redis键
            
        Returns:
            任务数据或None
        """
        try:
            if not self._client:
                await self.connect()
            
            data = await self._client.get(key)
            if data:
                return json.loads(data)
            return None
            
        except Exception as e:
            logger.error(f"获取任务数据失败 {key}: {e}")
            return None
    
    async def delete_task(self, key: str) -> bool:
        """删除任务数据
        
        Args:
            key: Redis键
            
        Returns:
            是否成功
        """
        try:
            if not self._client:
                await self.connect()
            
            result = await self._client.delete(key)
            return bool(result)
            
        except Exception as e:
            logger.error(f"删除任务数据失败 {key}: {e}")
            return False
    
    async def add_to_set(self, set_key: str, value: str) -> bool:
        """添加值到集合
        
        Args:
            set_key: 集合键
            value: 值
            
        Returns:
            是否成功
        """
        try:
            if not self._client:
                await self.connect()
            
            result = await self._client.sadd(set_key, value)
            return bool(result)
            
        except Exception as e:
            logger.error(f"添加到集合失败 {set_key}: {e}")
            return False
    
    async def remove_from_set(self, set_key: str, value: str) -> bool:
        """从集合中移除值
        
        Args:
            set_key: 集合键
            value: 值
            
        Returns:
            是否成功
        """
        try:
            if not self._client:
                await self.connect()
            
            result = await self._client.srem(set_key, value)
            return bool(result)
            
        except Exception as e:
            logger.error(f"从集合移除失败 {set_key}: {e}")
            return False
    
    async def get_set_members(self, set_key: str, limit: Optional[int] = None) -> list[str]:
        """获取集合成员
        
        Args:
            set_key: 集合键
            limit: 限制数量
            
        Returns:
            成员列表
        """
        try:
            if not self._client:
                await self.connect()
            
            members = await self._client.smembers(set_key)
            member_list = list(members)
            
            if limit and len(member_list) > limit:
                member_list = member_list[:limit]
            
            return member_list
            
        except Exception as e:
            logger.error(f"获取集合成员失败 {set_key}: {e}")
            return []
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在

        Args:
            key: Redis键

        Returns:
            是否存在
        """
        try:
            if not self._client:
                await self.connect()

            result = await self._client.exists(key)
            return bool(result)

        except Exception as e:
            logger.error(f"检查键存在失败 {key}: {e}")
            return False

    async def scan_keys(self, pattern: str) -> list[str]:
        """扫描匹配模式的键

        Args:
            pattern: 匹配模式

        Returns:
            匹配的键列表
        """
        try:
            if not self._client:
                await self.connect()

            keys = []
            cursor = 0
            while True:
                cursor, batch_keys = await self._client.scan(cursor, match=pattern, count=100)
                keys.extend(batch_keys)
                if cursor == 0:
                    break

            return keys

        except Exception as e:
            logger.error(f"扫描键失败 {pattern}: {e}")
            return []

    async def delete(self, key: str) -> bool:
        """删除键

        Args:
            key: Redis键

        Returns:
            是否成功
        """
        try:
            if not self._client:
                await self.connect()

            result = await self._client.delete(key)
            return bool(result)

        except Exception as e:
            logger.error(f"删除键失败 {key}: {e}")
            return False


# 全局Redis客户端实例
_redis_client: Optional[RedisClient] = None


async def get_redis_client() -> RedisClient:
    """获取Redis客户端实例"""
    global _redis_client
    if _redis_client is None:
        _redis_client = RedisClient()
        await _redis_client.connect()
    return _redis_client 