"""构建服务

提供远程构建、部署和插件编译功能。
"""

import asyncio
from datetime import datetime
from typing import Optional, TYPE_CHECKING
from models.task_models import TaskStatus, BuildTask
from models.response_models import TaskResponse
from core.logging import get_logger
from core.exceptions import TaskExecutionException
from config.settings import settings
from utils.task_manager import TaskManager
from .ssh_service import SSHService

if TYPE_CHECKING:
    from .credential_service import CredentialService

logger = get_logger(__name__)


class BuildService:
    """构建服务"""

    def __init__(self, task_manager: TaskManager, credential_service: Optional["CredentialService"] = None):
        self.task_manager = task_manager
        self.ssh_service = SSHService(credential_service)
    
    async def start_build_task(
        self,
        remote_ip: Optional[str] = None,
        ssh_user: Optional[str] = None,
        container_name: Optional[str] = None
    ) -> TaskResponse:
        """启动构建任务
        
        Args:
            remote_ip: 远程服务器IP
            ssh_user: SSH用户名
            container_name: 容器名称
            
        Returns:
            任务响应
        """
        # 使用默认值，处理空字符串
        remote_ip = remote_ip if remote_ip and remote_ip.strip() else settings.default_remote_ip
        ssh_user = ssh_user if ssh_user and ssh_user.strip() else settings.default_ssh_user
        container_name = container_name if container_name and container_name.strip() else settings.default_container_name
        
        # 创建任务
        task = await self.task_manager.create_build_task(
            remote_ip=remote_ip,
            ssh_user=ssh_user,
            container_name=container_name,
            message="编译任务已创建，正在后台执行"
        )
        
        # 异步执行构建
        asyncio.create_task(self._execute_build_task(task.task_id))
        
        return TaskResponse(
            task_id=task.task_id,
            status=task.status,
            message=task.message,
            created_at=task.created_at
        )
    
    async def start_deploy_task(
        self,
        remote_ip: Optional[str] = None,
        ssh_user: Optional[str] = None,
        container_name: Optional[str] = None
    ) -> TaskResponse:
        """启动部署任务

        Args:
            remote_ip: 远程服务器IP
            ssh_user: SSH用户名
            container_name: 容器名称

        Returns:
            任务响应
        """
        # 使用默认值，处理空字符串
        remote_ip = remote_ip if remote_ip and remote_ip.strip() else settings.default_remote_ip
        ssh_user = ssh_user if ssh_user and ssh_user.strip() else settings.default_ssh_user
        container_name = container_name if container_name and container_name.strip() else settings.default_container_name

        # 创建任务
        task = await self.task_manager.create_build_task(
            remote_ip=remote_ip,
            ssh_user=ssh_user,
            container_name=container_name,
            message="部署任务已创建，正在后台执行"
        )

        # 异步执行部署
        asyncio.create_task(self._execute_deploy_task(task.task_id))

        return TaskResponse(
            task_id=task.task_id,
            status=task.status,
            message=task.message,
            created_at=task.created_at
        )
    
    async def start_build_plugin_task(
        self,
        plugin_name: str,
        remote_ip: Optional[str] = None,
        ssh_user: Optional[str] = None,
        container_name: Optional[str] = None,
    ) -> TaskResponse:
        """启动插件编译任务（仅编译，不包含部署）

        Args:
            plugin_name: 插件名称（如：mongo_parser.so）
            remote_ip: 远程服务器IP
            ssh_user: SSH用户名
            container_name: 容器名称

        Returns:
            任务响应
        """
        # 使用默认值，处理空字符串
        remote_ip = remote_ip if remote_ip and remote_ip.strip() else settings.default_remote_ip
        ssh_user = ssh_user if ssh_user and ssh_user.strip() else settings.default_ssh_user
        container_name = container_name if container_name and container_name.strip() else settings.default_container_name

        # 根据插件名称自动生成路径
        plugin_base_name = plugin_name.replace('.so', '') if plugin_name.endswith('.so') else plugin_name
        container_base_path = settings.default_container_plugin_path.rsplit('/', 1)[0]
        container_plugin_path = f"{container_base_path}/{plugin_base_name}"

        # 创建任务
        task = await self.task_manager.create_build_task(
            remote_ip=remote_ip,
            ssh_user=ssh_user,
            container_name=container_name,
            message=f"插件编译任务已创建，正在后台执行",
            plugin_name=plugin_name,
            container_plugin_path=container_plugin_path
        )

        # 异步执行插件编译
        asyncio.create_task(self._execute_build_plugin_task(task.task_id))

        return TaskResponse(
            task_id=task.task_id,
            status=task.status,
            message=task.message,
            created_at=task.created_at
        )
    
    async def start_plugin_deploy_task(
        self,
        plugin_name: str,
        remote_ip: Optional[str] = None,
        ssh_user: Optional[str] = None,
        container_name: Optional[str] = None
    ) -> TaskResponse:
        """启动插件部署任务

        Args:
            plugin_name: 插件名称（如：mongo_parser.so）
            remote_ip: 远程服务器IP
            ssh_user: SSH用户名
            container_name: 容器名称

        Returns:
            任务响应
        """
        # 使用默认值，处理空字符串
        remote_ip = remote_ip if remote_ip and remote_ip.strip() else settings.default_remote_ip
        ssh_user = ssh_user if ssh_user and ssh_user.strip() else settings.default_ssh_user
        container_name = container_name if container_name and container_name.strip() else settings.default_container_name

        # 根据插件名称自动生成路径
        plugin_base_name = plugin_name.replace('.so', '') if plugin_name.endswith('.so') else plugin_name
        container_base_path = settings.default_container_plugin_path.rsplit('/', 1)[0]
        container_plugin_path = f"{container_base_path}/{plugin_base_name}"
        server_base_path = settings.default_server_plugin_path.rsplit('/', 1)[0]
        server_plugin_path = f"{server_base_path}/{plugin_base_name}"

        # 创建任务
        task = await self.task_manager.create_build_task(
            remote_ip=remote_ip,
            ssh_user=ssh_user,
            container_name=container_name,
            message="插件部署任务已创建，正在后台执行（复制+重启）",
            plugin_name=plugin_name,
            container_plugin_path=container_plugin_path,
            server_plugin_path=server_plugin_path
        )

        # 异步执行插件部署
        asyncio.create_task(self._execute_plugin_deploy_task(task.task_id))

        return TaskResponse(
            task_id=task.task_id,
            status=task.status,
            message=task.message,
            created_at=task.created_at
        )

    async def start_build_deploy_task(
        self,
        build_remote_ip: Optional[str] = None,
        build_ssh_user: Optional[str] = None,
        build_ssh_password: Optional[str] = None,
        container_name: Optional[str] = None,
        deploy_remote_ip: Optional[str] = None,
        deploy_ssh_user: Optional[str] = None,
        deploy_ssh_password: Optional[str] = None
    ) -> TaskResponse:
        """启动构建部署统一任务

        Args:
            build_remote_ip: 编译远程服务器IP
            build_ssh_user: 编译服务器SSH用户名
            build_ssh_password: 编译服务器SSH密码
            container_name: 容器名称
            deploy_remote_ip: 部署远程服务器IP
            deploy_ssh_user: 部署服务器SSH用户名
            deploy_ssh_password: 部署服务器SSH密码

        Returns:
            任务响应
        """
        # 使用默认值，处理空字符串
        build_remote_ip = build_remote_ip if build_remote_ip and build_remote_ip.strip() else settings.default_remote_ip
        build_ssh_user = build_ssh_user if build_ssh_user and build_ssh_user.strip() else settings.default_ssh_user
        container_name = container_name if container_name and container_name.strip() else settings.default_container_name
        deploy_remote_ip = deploy_remote_ip if deploy_remote_ip and deploy_remote_ip.strip() else settings.default_remote_ip
        deploy_ssh_user = deploy_ssh_user if deploy_ssh_user and deploy_ssh_user.strip() else settings.default_ssh_user

        # 创建任务
        task = await self.task_manager.create_build_task(
            remote_ip=build_remote_ip,
            ssh_user=build_ssh_user,
            container_name=container_name,
            message="构建部署任务已创建，正在后台执行（构建+部署hw_inst.zip包）",
            build_ssh_password=build_ssh_password,
            deploy_remote_ip=deploy_remote_ip,
            deploy_ssh_user=deploy_ssh_user,
            deploy_ssh_password=deploy_ssh_password
        )

        # 异步执行构建部署
        asyncio.create_task(self._execute_build_deploy_task(task.task_id))

        return TaskResponse(
            task_id=task.task_id,
            status=task.status,
            message=task.message,
            created_at=task.created_at
        )

    async def start_build_deploy_plugin_task(
        self,
        plugin_name: str,
        build_remote_ip: Optional[str] = None,
        build_ssh_user: Optional[str] = None,
        build_ssh_password: Optional[str] = None,
        container_name: Optional[str] = None,
        deploy_remote_ip: Optional[str] = None,
        deploy_ssh_user: Optional[str] = None,
        deploy_ssh_password: Optional[str] = None
    ) -> TaskResponse:
        """启动插件构建部署统一任务

        Args:
            plugin_name: 插件名称
            build_remote_ip: 编译远程服务器IP
            build_ssh_user: 编译服务器SSH用户名
            build_ssh_password: 编译服务器SSH密码
            container_name: 容器名称
            deploy_remote_ip: 部署远程服务器IP
            deploy_ssh_user: 部署服务器SSH用户名
            deploy_ssh_password: 部署服务器SSH密码

        Returns:
            任务响应
        """
        # 使用默认值，处理空字符串
        build_remote_ip = build_remote_ip if build_remote_ip and build_remote_ip.strip() else settings.default_remote_ip
        build_ssh_user = build_ssh_user if build_ssh_user and build_ssh_user.strip() else settings.default_ssh_user
        container_name = container_name if container_name and container_name.strip() else settings.default_container_name
        deploy_remote_ip = deploy_remote_ip if deploy_remote_ip and deploy_remote_ip.strip() else settings.default_remote_ip
        deploy_ssh_user = deploy_ssh_user if deploy_ssh_user and deploy_ssh_user.strip() else settings.default_ssh_user

        # 根据插件名称自动生成路径
        plugin_base_name = plugin_name.replace('.so', '') if plugin_name.endswith('.so') else plugin_name
        container_base_path = settings.default_container_plugin_path.rsplit('/', 1)[0]
        container_plugin_path = f"{container_base_path}/{plugin_base_name}"
        server_base_path = settings.default_server_plugin_path.rsplit('/', 1)[0]
        server_plugin_path = f"{server_base_path}/{plugin_base_name}"

        # 创建任务
        task = await self.task_manager.create_build_task(
            remote_ip=build_remote_ip,
            ssh_user=build_ssh_user,
            container_name=container_name,
            message="插件构建部署任务已创建，正在后台执行（编译+部署+重启）",
            plugin_name=plugin_name,
            container_plugin_path=container_plugin_path,
            server_plugin_path=server_plugin_path,
            build_ssh_password=build_ssh_password,
            deploy_remote_ip=deploy_remote_ip,
            deploy_ssh_user=deploy_ssh_user,
            deploy_ssh_password=deploy_ssh_password
        )

        # 异步执行插件构建部署
        asyncio.create_task(self._execute_build_deploy_plugin_task(task.task_id))

        return TaskResponse(
            task_id=task.task_id,
            status=task.status,
            message=task.message,
            created_at=task.created_at
        )
    
    async def _execute_build_task(self, task_id: str) -> None:
        """执行构建任务
        
        Args:
            task_id: 任务ID
        """
        task = None
        try:
            # 获取任务
            task = await self.task_manager.get_build_task(task_id)
            
            # 更新任务状态为运行中
            task.status = TaskStatus.RUNNING
            task.started_at = datetime.now()
            task.message = "正在执行编译脚本"
            await self.task_manager.update_build_task(task)
            
            logger.info(f"开始执行构建任务: {task_id}")
            
            # 检查容器是否运行
            if not await self.ssh_service.check_container_running(
                task.remote_ip, task.ssh_user, task.container_name
            ):
                raise TaskExecutionException(task_id, f"容器 {task.container_name} 未运行")
            
            # 执行构建脚本
            command = f"docker exec {task.container_name} bash -c 'cd {settings.default_build_script_path} && git pull && sh build.sh'"
            stdout, stderr, return_code = await self.ssh_service.execute_command(
                task.remote_ip, task.ssh_user, command, timeout=1800  # 30分钟超时
            )
            
            # 更新任务结果
            task.stdout = stdout
            task.stderr = stderr
            task.return_code = return_code
            task.completed_at = datetime.now()
            
            if return_code == 0:
                task.status = TaskStatus.SUCCESS
                task.message = "编译脚本执行成功"
                logger.info(f"构建任务成功: {task_id}")
            else:
                task.status = TaskStatus.FAILED
                task.message = f"编译脚本执行失败，返回码: {return_code}"
                logger.error(f"构建任务失败: {task_id}, 返回码: {return_code}")
            
            await self.task_manager.update_build_task(task)
            
        except Exception as e:
            logger.error(f"构建任务执行异常: {task_id}, {e}")
            try:
                if task is None:
                    task = await self.task_manager.get_build_task(task_id)

                task.status = TaskStatus.ERROR
                task.message = f"任务执行异常: {str(e)}"
                task.completed_at = datetime.now()
                await self.task_manager.update_build_task(task)
            except Exception as update_err:
                logger.error(f"更新失败任务状态时发生错误: {task_id}, {update_err}")
                pass
    
    async def _execute_deploy_task(self, task_id: str) -> None:
        """执行部署任务

        Args:
            task_id: 任务ID
        """
        task = None
        all_output = []
        all_errors = []
        try:
            # 获取任务
            task = await self.task_manager.get_build_task(task_id)

            # 更新任务状态为运行中
            task.status = TaskStatus.RUNNING
            task.started_at = datetime.now()
            task.message = "正在执行hw_inst.zip包部署流程"
            await self.task_manager.update_build_task(task)

            logger.info(f"开始执行部署任务: {task_id}")

            # 检查容器是否运行
            if not await self.ssh_service.check_container_running(
                task.remote_ip, task.ssh_user, task.container_name
            ):
                raise TaskExecutionException(task_id, f"容器 {task.container_name} 未运行")

            # 步骤1: 复制hw_inst.zip包到目标服务器的/tmp目录
            logger.info(f"步骤1: 复制hw_inst.zip包到目标服务器")
            command = f"docker cp {task.container_name}:/home/<USER>/build/out/hw_inst.zip /tmp/"
            stdout, stderr, return_code = await self.ssh_service.execute_command(
                task.remote_ip, task.ssh_user, command, timeout=300  # 5分钟超时
            )

            all_output.append(f"=== 复制hw_inst.zip包 ===\n{stdout}")
            all_errors.append(f"=== 复制hw_inst.zip包错误 ===\n{stderr}")

            if return_code != 0:
                task.return_code = return_code
                raise TaskExecutionException(task_id, f"复制hw_inst.zip包失败，返回码: {return_code}")

            # 步骤2: 解压并执行安装脚本
            logger.info(f"步骤2: 解压并执行安装脚本")
            command = "cd /tmp && unzip -o hw_inst.zip && cd inst && bash inst.sh -t Compatible -p API -v 3.3"
            stdout, stderr, return_code = await self.ssh_service.execute_command(
                task.remote_ip, task.ssh_user, command, timeout=300  # 5分钟超时
            )

            all_output.append(f"=== 解压并执行安装脚本 ===\n{stdout}")
            all_errors.append(f"=== 解压并执行安装脚本错误 ===\n{stderr}")

            if return_code != 0:
                task.return_code = return_code
                raise TaskExecutionException(task_id, f"解压并执行安装脚本失败，返回码: {return_code}")

            # 步骤3: 清理临时文件
            logger.info(f"步骤3: 清理临时文件")
            command = "cd /tmp && rm -rf hw_inst.zip inst"
            stdout, stderr, return_code = await self.ssh_service.execute_command(
                task.remote_ip, task.ssh_user, command, timeout=60
            )

            all_output.append(f"=== 清理临时文件 ===\n{stdout}")
            all_errors.append(f"=== 清理临时文件错误 ===\n{stderr}")

            # 清理失败不影响整体部署结果，只记录日志
            if return_code != 0:
                logger.warning(f"清理临时文件失败，返回码: {return_code}")

            # 更新任务结果
            task.stdout = "\n".join(all_output)
            task.stderr = "\n".join(all_errors)
            task.return_code = 0
            task.completed_at = datetime.now()
            task.status = TaskStatus.SUCCESS
            task.message = "hw_inst.zip包部署成功"

            await self.task_manager.update_build_task(task)
            logger.info(f"部署任务成功: {task_id}")

        except Exception as e:
            logger.error(f"部署任务执行异常: {task_id}, {e}")
            try:
                if task is None:
                    task = await self.task_manager.get_build_task(task_id)

                task.status = TaskStatus.ERROR
                task.message = f"部署任务执行异常: {str(e)}"
                task.completed_at = datetime.now()
                # 在异常情况下，保存已经捕获到的输出
                task.stdout = "\n".join(all_output)
                task.stderr = "\n".join(all_errors)
                await self.task_manager.update_build_task(task)
            except Exception as update_err:
                logger.error(f"更新失败任务状态时发生错误: {task_id}, {update_err}")
                pass
    
    async def _execute_build_plugin_task(self, task_id: str) -> None:
        """执行插件编译任务

        Args:
            task_id: 任务ID
        """
        task = None
        all_output = []
        all_errors = []
        try:
            # 获取任务
            task = await self.task_manager.get_build_task(task_id)

            # 更新任务状态为运行中
            task.status = TaskStatus.RUNNING
            task.started_at = datetime.now()
            task.message = "正在编译插件"
            await self.task_manager.update_build_task(task)

            logger.info(f"开始执行插件编译任务: {task_id}")

            # 检查容器是否运行
            if not await self.ssh_service.check_container_running(
                task.remote_ip, task.ssh_user, task.container_name
            ):
                raise TaskExecutionException(task_id, f"容器 {task.container_name} 未运行")

            # 执行插件编译（仅编译，不包含部署逻辑）
            # 根据任务参数确定编译路径和命令
            command = f"docker exec {task.container_name} bash -c 'cd {task.container_plugin_path} && git pull && make BUILD_ARCH=x86 -j{settings.default_make_jobs}'"

            logger.info(f"开始编译插件，路径: {task.container_plugin_path}")
            stdout, stderr, return_code = await self.ssh_service.execute_command(
                task.remote_ip, task.ssh_user, command, timeout=300  # 5分钟超时
            )

            all_output.append(f"=== 编译插件 ===\n{stdout}")
            all_errors.append(f"=== 编译插件错误 ===\n{stderr}")

            if return_code != 0:
                task.return_code = return_code
                raise TaskExecutionException(task_id, f"插件编译失败，返回码: {return_code}")

            task.message = "插件编译成功"

            # 更新任务结果
            task.stdout = "\n".join(all_output)
            task.stderr = "\n".join(all_errors)
            task.return_code = 0
            task.completed_at = datetime.now()
            task.status = TaskStatus.SUCCESS

            await self.task_manager.update_build_task(task)
            logger.info(f"插件编译任务成功: {task_id}")

        except Exception as e:
            logger.error(f"插件编译任务执行异常: {task_id}, {e}")
            try:
                if task is None:
                    task = await self.task_manager.get_build_task(task_id)

                task.status = TaskStatus.ERROR
                task.message = f"插件编译任务执行异常: {str(e)}"
                task.completed_at = datetime.now()
                # 在异常情况下，保存已经捕获到的输出
                task.stdout = "\n".join(all_output)
                task.stderr = "\n".join(all_errors)
                await self.task_manager.update_build_task(task)
            except Exception as update_err:
                logger.error(f"更新失败任务状态时发生错误: {task_id}, {update_err}")
                pass

    async def _execute_plugin_deploy_task(self, task_id: str) -> None:
        """执行插件部署任务

        Args:
            task_id: 任务ID
        """
        task = None
        all_output = []
        all_errors = []
        try:
            # 获取任务
            task = await self.task_manager.get_build_task(task_id)

            # 更新任务状态为运行中
            task.status = TaskStatus.RUNNING
            task.started_at = datetime.now()
            task.message = "正在执行插件部署流程"
            await self.task_manager.update_build_task(task)

            logger.info(f"开始执行插件部署任务: {task_id}")

            # 检查容器是否运行
            if not await self.ssh_service.check_container_running(
                task.remote_ip, task.ssh_user, task.container_name
            ):
                raise TaskExecutionException(task_id, f"容器 {task.container_name} 未运行")

            # 步骤1: 复制插件文件
            logger.info(f"步骤1: 复制插件文件到服务器")
            command = f"docker cp {task.container_name}:{task.container_plugin_path}/{task.plugin_name} {task.server_plugin_path}"
            stdout, stderr, return_code = await self.ssh_service.execute_command(
                task.remote_ip, task.ssh_user, command, timeout=60
            )

            all_output.append(f"=== 复制插件文件 ===\n{stdout}")
            all_errors.append(f"=== 复制插件文件错误 ===\n{stderr}")

            if return_code != 0:
                task.return_code = return_code
                raise TaskExecutionException(task_id, f"插件文件复制失败，返回码: {return_code}")

            # 步骤2: 重启gwhw服务
            logger.info(f"步骤2: 重启gwhw服务")
            command = "systemctl restart gwhw"
            stdout, stderr, return_code = await self.ssh_service.execute_command(
                task.remote_ip, task.ssh_user, command, timeout=60
            )

            all_output.append(f"=== 重启服务 ===\n{stdout}")
            all_errors.append(f"=== 重启服务错误 ===\n{stderr}")

            if return_code != 0:
                task.return_code = return_code
                raise TaskExecutionException(task_id, f"服务重启失败，返回码: {return_code}")

            # 更新任务结果
            task.stdout = "\n".join(all_output)
            task.stderr = "\n".join(all_errors)
            task.return_code = 0
            task.completed_at = datetime.now()
            task.status = TaskStatus.SUCCESS
            task.message = "插件部署成功（复制+重启）"

            await self.task_manager.update_build_task(task)
            logger.info(f"插件部署任务成功: {task_id}")

        except Exception as e:
            logger.error(f"插件部署任务执行异常: {task_id}, {e}")
            try:
                if task is None:
                    task = await self.task_manager.get_build_task(task_id)

                task.status = TaskStatus.ERROR
                task.message = f"插件部署任务执行异常: {str(e)}"
                task.completed_at = datetime.now()
                # 在异常情况下，保存已经捕获到的输出
                task.stdout = "\n".join(all_output)
                task.stderr = "\n".join(all_errors)
                await self.task_manager.update_build_task(task)
            except Exception as update_err:
                logger.error(f"更新失败任务状态时发生错误: {task_id}, {update_err}")
                pass

    async def _execute_build_deploy_task(self, task_id: str) -> None:
        """执行构建部署统一任务

        Args:
            task_id: 任务ID
        """
        task = None
        all_output = []
        all_errors = []
        try:
            # 获取任务
            task = await self.task_manager.get_build_task(task_id)

            # 更新任务状态为运行中
            task.status = TaskStatus.RUNNING
            task.started_at = datetime.now()
            task.message = "正在执行构建部署流程"
            await self.task_manager.update_build_task(task)

            logger.info(f"开始执行构建部署任务: {task_id}")

            # 步骤1: 在编译服务器上执行构建
            logger.info(f"步骤1: 在编译服务器上执行构建")
            build_ssh_password = getattr(task, 'build_ssh_password', None)
            if not await self.ssh_service.check_container_running(
                task.remote_ip, task.ssh_user, task.container_name
            ):
                raise TaskExecutionException(task_id, f"容器 {task.container_name} 未运行")

            # 执行构建脚本
            command = f"docker exec {task.container_name} bash -c 'cd {settings.default_build_script_path} && git pull && sh build.sh'"
            stdout, stderr, return_code = await self.ssh_service.execute_command(
                task.remote_ip, task.ssh_user, command, timeout=1800, password=build_ssh_password  # 30分钟超时
            )

            all_output.append(f"=== 构建阶段 ===\n{stdout}")
            all_errors.append(f"=== 构建阶段错误 ===\n{stderr}")

            if return_code != 0:
                task.return_code = return_code
                raise TaskExecutionException(task_id, f"构建失败，返回码: {return_code}")

            # 步骤2: 复制hw_inst.zip包到部署服务器
            logger.info(f"步骤2: 复制hw_inst.zip包到部署服务器")
            deploy_remote_ip = getattr(task, 'deploy_remote_ip', task.remote_ip)
            deploy_ssh_user = getattr(task, 'deploy_ssh_user', task.ssh_user)
            deploy_ssh_password = getattr(task, 'deploy_ssh_password', None)

            # 先从容器复制到编译服务器
            command = f"docker cp {task.container_name}:/home/<USER>/build/out/hw_inst.zip /tmp/"
            stdout, stderr, return_code = await self.ssh_service.execute_command(
                task.remote_ip, task.ssh_user, command, timeout=300, password=build_ssh_password
            )

            all_output.append(f"=== 复制到编译服务器 ===\n{stdout}")
            all_errors.append(f"=== 复制到编译服务器错误 ===\n{stderr}")

            if return_code != 0:
                task.return_code = return_code
                raise TaskExecutionException(task_id, f"复制hw_inst.zip到编译服务器失败，返回码: {return_code}")

            # 如果部署服务器与编译服务器不同，需要传输文件
            if deploy_remote_ip != task.remote_ip:
                logger.info(f"步骤2.1: 传输文件到部署服务器 {deploy_remote_ip}")

                # 使用服务器间文件传输
                success = await self.ssh_service.transfer_file_between_servers(
                    source_host=task.remote_ip,
                    source_user=task.ssh_user,
                    source_path="/tmp/hw_inst.zip",
                    dest_host=deploy_remote_ip,
                    dest_user=deploy_ssh_user,
                    dest_path="/tmp/hw_inst.zip",
                    source_password=build_ssh_password,
                    dest_password=deploy_ssh_password
                )

                if not success:
                    raise TaskExecutionException(task_id, "文件传输到部署服务器失败")

                all_output.append(f"=== 文件传输到部署服务器 ===\n文件传输成功")
                logger.info("文件传输到部署服务器成功")

            # 步骤3: 在部署服务器上解压并执行安装
            logger.info(f"步骤3: 在部署服务器上解压并执行安装")
            command = "cd /tmp && unzip -o hw_inst.zip && cd inst && bash inst.sh -t Compatible -p API -v 3.3"
            stdout, stderr, return_code = await self.ssh_service.execute_command(
                deploy_remote_ip, deploy_ssh_user, command, timeout=1800, password=deploy_ssh_password  # 30分钟超时
            )

            all_output.append(f"=== 部署阶段 ===\n{stdout}")
            all_errors.append(f"=== 部署阶段错误 ===\n{stderr}")

            if return_code != 0:
                task.return_code = return_code
                raise TaskExecutionException(task_id, f"部署失败，返回码: {return_code}")

            # 步骤4: 清理临时文件
            logger.info(f"步骤4: 清理临时文件")
            command = "cd /tmp && rm -rf hw_inst.zip inst"
            stdout, stderr, return_code = await self.ssh_service.execute_command(
                deploy_remote_ip, deploy_ssh_user, command, timeout=60, password=deploy_ssh_password
            )

            all_output.append(f"=== 清理临时文件 ===\n{stdout}")
            all_errors.append(f"=== 清理临时文件错误 ===\n{stderr}")

            # 清理失败不影响整体结果
            if return_code != 0:
                logger.warning(f"清理临时文件失败，返回码: {return_code}")

            # 更新任务结果
            task.stdout = "\n".join(all_output)
            task.stderr = "\n".join(all_errors)
            task.return_code = 0
            task.completed_at = datetime.now()
            task.status = TaskStatus.SUCCESS
            task.message = "构建部署任务执行成功"

            await self.task_manager.update_build_task(task)
            logger.info(f"构建部署任务成功: {task_id}")

        except Exception as e:
            logger.error(f"构建部署任务执行异常: {task_id}, {e}")
            try:
                if task is None:
                    task = await self.task_manager.get_build_task(task_id)

                task.status = TaskStatus.ERROR
                task.message = f"构建部署任务执行异常: {str(e)}"
                task.completed_at = datetime.now()
                # 在异常情况下，保存已经捕获到的输出
                task.stdout = "\n".join(all_output)
                task.stderr = "\n".join(all_errors)
                await self.task_manager.update_build_task(task)
            except Exception as update_err:
                logger.error(f"更新失败任务状态时发生错误: {task_id}, {update_err}")
                pass

    async def _execute_build_deploy_plugin_task(self, task_id: str) -> None:
        """执行插件构建部署统一任务

        Args:
            task_id: 任务ID
        """
        task = None
        all_output = []
        all_errors = []
        try:
            # 获取任务
            task = await self.task_manager.get_build_task(task_id)

            # 更新任务状态为运行中
            task.status = TaskStatus.RUNNING
            task.started_at = datetime.now()
            task.message = "正在执行插件构建部署流程"
            await self.task_manager.update_build_task(task)

            logger.info(f"开始执行插件构建部署任务: {task_id}")

            # 步骤1: 在编译服务器上编译插件
            logger.info(f"步骤1: 在编译服务器上编译插件 {task.plugin_name}")
            build_ssh_password = getattr(task, 'build_ssh_password', None)
            if not await self.ssh_service.check_container_running(
                task.remote_ip, task.ssh_user, task.container_name
            ):
                raise TaskExecutionException(task_id, f"容器 {task.container_name} 未运行")

            # 执行插件编译
            command = f"docker exec {task.container_name} bash -c 'cd {task.container_plugin_path} && git pull && make BUILD_ARCH=x86 -j{settings.default_make_jobs}'"

            stdout, stderr, return_code = await self.ssh_service.execute_command(
                task.remote_ip, task.ssh_user, command, timeout=300, password=build_ssh_password  # 5分钟超时
            )

            all_output.append(f"=== 编译插件 ===\n{stdout}")
            all_errors.append(f"=== 编译插件错误 ===\n{stderr}")

            if return_code != 0:
                task.return_code = return_code
                raise TaskExecutionException(task_id, f"插件编译失败，返回码: {return_code}")

            # 步骤2: 复制插件文件到部署服务器
            logger.info(f"步骤2: 复制插件文件到部署服务器")
            deploy_remote_ip = getattr(task, 'deploy_remote_ip', task.remote_ip)
            deploy_ssh_user = getattr(task, 'deploy_ssh_user', task.ssh_user)
            deploy_ssh_password = getattr(task, 'deploy_ssh_password', None)

            # 从容器复制插件文件
            source_path = f"{task.container_plugin_path}/{task.plugin_name}"

            if deploy_remote_ip == task.remote_ip:
                # 同一服务器，直接复制
                command = f"docker cp {task.container_name}:{source_path} {task.server_plugin_path}"
                stdout, stderr, return_code = await self.ssh_service.execute_command(
                    task.remote_ip, task.ssh_user, command, timeout=60, password=build_ssh_password
                )
                all_output.append(f"=== 复制插件文件 ===\n{stdout}")
                all_errors.append(f"=== 复制插件文件错误 ===\n{stderr}")
            else:
                # 不同服务器，需要先复制到编译服务器，再传输到部署服务器
                # 步骤2.1: 复制到编译服务器临时目录
                command = f"docker cp {task.container_name}:{source_path} /tmp/{task.plugin_name}"
                stdout, stderr, return_code = await self.ssh_service.execute_command(
                    task.remote_ip, task.ssh_user, command, timeout=60, password=build_ssh_password
                )

                all_output.append(f"=== 复制到编译服务器 ===\n{stdout}")
                all_errors.append(f"=== 复制到编译服务器错误 ===\n{stderr}")

                if return_code == 0:
                    # 步骤2.2: 传输到部署服务器
                    logger.info(f"步骤2.2: 传输插件文件到部署服务器")
                    success = await self.ssh_service.transfer_file_between_servers(
                        source_host=task.remote_ip,
                        source_user=task.ssh_user,
                        source_path=f"/tmp/{task.plugin_name}",
                        dest_host=deploy_remote_ip,
                        dest_user=deploy_ssh_user,
                        dest_path=task.server_plugin_path,
                        source_password=build_ssh_password,
                        dest_password=deploy_ssh_password
                    )

                    if success:
                        all_output.append(f"=== 传输插件文件到部署服务器 ===\n文件传输成功")
                        return_code = 0
                    else:
                        all_errors.append(f"=== 传输插件文件到部署服务器错误 ===\n文件传输失败")
                        return_code = 1

            if return_code != 0:
                task.return_code = return_code
                raise TaskExecutionException(task_id, f"插件文件复制失败，返回码: {return_code}")

            # 步骤3: 重启gwhw服务
            logger.info(f"步骤3: 重启gwhw服务")
            command = "systemctl restart gwhw"
            stdout, stderr, return_code = await self.ssh_service.execute_command(
                deploy_remote_ip, deploy_ssh_user, command, timeout=60, password=deploy_ssh_password
            )

            all_output.append(f"=== 重启服务 ===\n{stdout}")
            all_errors.append(f"=== 重启服务错误 ===\n{stderr}")

            if return_code != 0:
                task.return_code = return_code
                raise TaskExecutionException(task_id, f"服务重启失败，返回码: {return_code}")

            # 更新任务结果
            task.stdout = "\n".join(all_output)
            task.stderr = "\n".join(all_errors)
            task.return_code = 0
            task.completed_at = datetime.now()
            task.status = TaskStatus.SUCCESS
            task.message = "插件构建部署任务执行成功"

            await self.task_manager.update_build_task(task)
            logger.info(f"插件构建部署任务成功: {task_id}")

        except Exception as e:
            logger.error(f"插件构建部署任务执行异常: {task_id}, {e}")
            try:
                if task is None:
                    task = await self.task_manager.get_build_task(task_id)

                task.status = TaskStatus.ERROR
                task.message = f"插件构建部署任务执行异常: {str(e)}"
                task.completed_at = datetime.now()
                # 在异常情况下，保存已经捕获到的输出
                task.stdout = "\n".join(all_output)
                task.stderr = "\n".join(all_errors)
                await self.task_manager.update_build_task(task)
            except Exception as update_err:
                logger.error(f"更新失败任务状态时发生错误: {task_id}, {update_err}")
                pass