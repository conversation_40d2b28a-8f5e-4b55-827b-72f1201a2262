"""服务器凭据管理服务

提供服务器凭据的存储、检索和管理功能。
"""

import json
from datetime import datetime
from typing import Optional, Any
from core.logging import get_logger
from core.exceptions import CredentialNotFoundException, CredentialAlreadyExistsException
from config.settings import settings
from utils.redis_client import RedisClient
from utils.crypto_utils import CryptoUtils
from models.credential_models import (
    ServerCredential,
    CredentialCreateRequest,
    CredentialUpdateRequest,
    CredentialResponse,
    CredentialListResponse,
    CredentialBatchImportRequest,
    CredentialBatchImportResponse,
    CredentialBackupData
)

logger = get_logger(__name__)


class CredentialService:
    """服务器凭据管理服务"""
    
    def __init__(self, redis_client: RedisClient):
        self.redis_client = redis_client
        self.redis_prefix = settings.credential_redis_prefix
        self.expire_time = settings.credential_expire_time
        self.enable_audit = settings.enable_credential_audit
        
        # 初始化加密器
        self._init_encryption()
    
    def _init_encryption(self):
        """初始化加密器"""
        try:
            # 验证加密密钥
            if not CryptoUtils.validate_key(settings.credential_encryption_key):
                logger.warning("加密密钥无效，使用默认密钥")
                # 生成一个临时密钥用于测试
                self.encryption_key = CryptoUtils.generate_fernet_key()
            else:
                self.encryption_key = settings.credential_encryption_key

            logger.info("凭据加密器初始化成功")

        except Exception as e:
            logger.error(f"初始化加密器失败: {e}")
            raise

    def _encrypt_password(self, password: str) -> str:
        """加密密码"""
        try:
            return CryptoUtils.encrypt_password(password, self.encryption_key)
        except Exception as e:
            logger.error(f"密码加密失败: {e}")
            raise

    def _decrypt_password(self, encrypted_password: str) -> str:
        """解密密码"""
        try:
            return CryptoUtils.decrypt_password(encrypted_password, self.encryption_key)
        except Exception as e:
            logger.error(f"密码解密失败: {e}")
            raise
    
    def _get_credential_key(self, server_ip: str) -> str:
        """获取凭据的Redis键"""
        return f"{self.redis_prefix}:{server_ip}"
    
    def _log_audit(self, action: str, server_ip: str, details: str = ""):
        """记录审计日志"""
        if self.enable_audit:
            audit_log = {
                "action": action,
                "server_ip": server_ip,
                "timestamp": datetime.now().isoformat(),
                "details": details
            }
            logger.info(f"凭据操作审计: {json.dumps(audit_log, ensure_ascii=False)}")
    
    async def create_credential(self, request: CredentialCreateRequest) -> CredentialResponse:
        """创建服务器凭据"""
        try:
            # 检查凭据是否已存在
            if await self.credential_exists(request.server_ip):
                raise CredentialAlreadyExistsException(request.server_ip)
            
            # 创建凭据对象
            now = datetime.now()
            credential = ServerCredential(
                server_ip=request.server_ip,
                ssh_user=request.ssh_user,
                ssh_password=self._encrypt_password(request.ssh_password),
                description=request.description or "",
                created_at=now,
                updated_at=now
            )
            
            # 存储到Redis
            key = self._get_credential_key(request.server_ip)
            credential_data = credential.dict()
            
            success = await self.redis_client.set_task(
                key, credential_data, expire=self.expire_time
            )
            
            if not success:
                raise Exception("存储凭据到Redis失败")
            
            # 记录审计日志
            self._log_audit("CREATE", request.server_ip, f"用户: {request.ssh_user}")
            
            logger.info(f"成功创建服务器凭据: {request.server_ip}")
            
            # 返回响应（不包含密码）
            return CredentialResponse(
                server_ip=credential.server_ip,
                ssh_user=credential.ssh_user,
                description=credential.description,
                created_at=credential.created_at,
                updated_at=credential.updated_at
            )
            
        except CredentialAlreadyExistsException:
            raise
        except Exception as e:
            logger.error(f"创建凭据失败 {request.server_ip}: {e}")
            raise
    
    async def get_credential(self, server_ip: str, include_password: bool = False) -> Optional[ServerCredential]:
        """获取服务器凭据"""
        try:
            key = self._get_credential_key(server_ip)
            credential_data = await self.redis_client.get_task(key)
            
            if not credential_data:
                return None
            
            # 解析凭据数据
            credential = ServerCredential(**credential_data)
            
            # 如果需要密码，解密密码
            if include_password:
                credential.ssh_password = self._decrypt_password(credential.ssh_password)
            
            return credential
            
        except Exception as e:
            logger.error(f"获取凭据失败 {server_ip}: {e}")
            return None
    
    async def credential_exists(self, server_ip: str) -> bool:
        """检查凭据是否存在"""
        try:
            key = self._get_credential_key(server_ip)
            return await self.redis_client.exists(key)
        except Exception as e:
            logger.error(f"检查凭据存在性失败 {server_ip}: {e}")
            return False

    async def update_credential(self, server_ip: str, request: CredentialUpdateRequest) -> CredentialResponse:
        """更新服务器凭据"""
        try:
            # 获取现有凭据
            existing_credential = await self.get_credential(server_ip, include_password=True)
            if not existing_credential:
                raise CredentialNotFoundException(server_ip)

            # 更新字段
            now = datetime.now()
            if request.ssh_user is not None:
                existing_credential.ssh_user = request.ssh_user
            if request.ssh_password is not None:
                existing_credential.ssh_password = self._encrypt_password(request.ssh_password)
            if request.description is not None:
                existing_credential.description = request.description
            existing_credential.updated_at = now

            # 存储更新后的凭据
            key = self._get_credential_key(server_ip)
            credential_data = existing_credential.dict()

            success = await self.redis_client.set_task(
                key, credential_data, expire=self.expire_time
            )

            if not success:
                raise Exception("更新凭据到Redis失败")

            # 记录审计日志
            changes = []
            if request.ssh_user is not None:
                changes.append(f"用户名: {request.ssh_user}")
            if request.ssh_password is not None:
                changes.append("密码已更新")
            if request.description is not None:
                changes.append(f"描述: {request.description}")

            self._log_audit("UPDATE", server_ip, f"更新: {', '.join(changes)}")

            logger.info(f"成功更新服务器凭据: {server_ip}")

            # 返回响应（不包含密码）
            return CredentialResponse(
                server_ip=existing_credential.server_ip,
                ssh_user=existing_credential.ssh_user,
                description=existing_credential.description,
                created_at=existing_credential.created_at,
                updated_at=existing_credential.updated_at
            )

        except CredentialNotFoundException:
            raise
        except Exception as e:
            logger.error(f"更新凭据失败 {server_ip}: {e}")
            raise

    async def delete_credential(self, server_ip: str) -> bool:
        """删除服务器凭据"""
        try:
            # 检查凭据是否存在
            if not await self.credential_exists(server_ip):
                raise CredentialNotFoundException(server_ip)

            # 删除凭据
            key = self._get_credential_key(server_ip)
            success = await self.redis_client.delete(key)

            if success:
                # 记录审计日志
                self._log_audit("DELETE", server_ip)
                logger.info(f"成功删除服务器凭据: {server_ip}")
                return True
            else:
                logger.error(f"删除凭据失败: {server_ip}")
                return False

        except CredentialNotFoundException:
            raise
        except Exception as e:
            logger.error(f"删除凭据失败 {server_ip}: {e}")
            return False

    async def list_credentials(self) -> CredentialListResponse:
        """获取所有服务器凭据列表"""
        try:
            # 获取所有凭据键
            pattern = f"{self.redis_prefix}:*"
            keys = await self.redis_client.scan_keys(pattern)

            credentials = []
            for key in keys:
                credential_data = await self.redis_client.get_task(key)
                if credential_data:
                    credential = ServerCredential(**credential_data)
                    # 添加到列表（不包含密码）
                    credentials.append(CredentialResponse(
                        server_ip=credential.server_ip,
                        ssh_user=credential.ssh_user,
                        description=credential.description,
                        created_at=credential.created_at,
                        updated_at=credential.updated_at
                    ))

            # 按IP地址排序
            credentials.sort(key=lambda x: x.server_ip)

            logger.info(f"获取凭据列表成功，共 {len(credentials)} 条")

            return CredentialListResponse(
                credentials=credentials,
                total=len(credentials),
                message="获取凭据列表成功"
            )

        except Exception as e:
            logger.error(f"获取凭据列表失败: {e}")
            return CredentialListResponse(
                credentials=[],
                total=0,
                message=f"获取凭据列表失败: {str(e)}"
            )

    async def batch_import_credentials(self, request: CredentialBatchImportRequest) -> CredentialBatchImportResponse:
        """批量导入凭据"""
        success_count = 0
        failed_count = 0
        failed_items = []

        try:
            for credential_request in request.credentials:
                try:
                    # 检查是否已存在
                    exists = await self.credential_exists(credential_request.server_ip)
                    if exists and not request.overwrite_existing:
                        failed_count += 1
                        failed_items.append(f"{credential_request.server_ip}: 凭据已存在")
                        continue

                    # 如果存在且允许覆盖，先删除
                    if exists and request.overwrite_existing:
                        await self.delete_credential(credential_request.server_ip)

                    # 创建凭据
                    await self.create_credential(credential_request)
                    success_count += 1

                except Exception as e:
                    failed_count += 1
                    failed_items.append(f"{credential_request.server_ip}: {str(e)}")
                    logger.error(f"导入凭据失败 {credential_request.server_ip}: {e}")

            # 记录审计日志
            self._log_audit("BATCH_IMPORT", "multiple",
                          f"成功: {success_count}, 失败: {failed_count}")

            logger.info(f"批量导入完成，成功: {success_count}, 失败: {failed_count}")

            return CredentialBatchImportResponse(
                success_count=success_count,
                failed_count=failed_count,
                failed_items=failed_items,
                message=f"批量导入完成，成功: {success_count}, 失败: {failed_count}"
            )

        except Exception as e:
            logger.error(f"批量导入凭据失败: {e}")
            return CredentialBatchImportResponse(
                success_count=success_count,
                failed_count=failed_count,
                failed_items=failed_items,
                message=f"批量导入失败: {str(e)}"
            )

    async def backup_credentials(self) -> CredentialBackupData:
        """备份所有凭据"""
        try:
            # 获取所有凭据键
            pattern = f"{self.redis_prefix}:*"
            keys = await self.redis_client.scan_keys(pattern)

            credentials = []
            for key in keys:
                credential_data = await self.redis_client.get_task(key)
                if credential_data:
                    credential = ServerCredential(**credential_data)
                    credentials.append(credential)

            backup_data = CredentialBackupData(
                credentials=credentials,
                backup_time=datetime.now(),
                version="1.0"
            )

            # 记录审计日志
            self._log_audit("BACKUP", "all", f"备份 {len(credentials)} 条凭据")

            logger.info(f"凭据备份成功，共 {len(credentials)} 条")
            return backup_data

        except Exception as e:
            logger.error(f"凭据备份失败: {e}")
            raise

    async def restore_credentials(self, backup_data: CredentialBackupData, overwrite_existing: bool = False) -> CredentialBatchImportResponse:
        """从备份恢复凭据"""
        try:
            # 转换为批量导入请求
            import_requests = []
            for credential in backup_data.credentials:
                # 解密密码用于恢复
                decrypted_password = self._decrypt_password(credential.ssh_password)
                import_requests.append(CredentialCreateRequest(
                    server_ip=credential.server_ip,
                    ssh_user=credential.ssh_user,
                    ssh_password=decrypted_password,
                    description=credential.description
                ))

            batch_request = CredentialBatchImportRequest(
                credentials=import_requests,
                overwrite_existing=overwrite_existing
            )

            # 执行批量导入
            result = await self.batch_import_credentials(batch_request)

            # 记录审计日志
            self._log_audit("RESTORE", "all",
                          f"恢复完成，成功: {result.success_count}, 失败: {result.failed_count}")

            logger.info(f"凭据恢复完成，成功: {result.success_count}, 失败: {result.failed_count}")
            return result

        except Exception as e:
            logger.error(f"凭据恢复失败: {e}")
            raise

    async def get_credential_for_ssh(self, server_ip: str) -> Optional[dict[str, str]]:
        """获取用于SSH连接的凭据信息"""
        try:
            credential = await self.get_credential(server_ip, include_password=True)
            if credential:
                return {
                    "ssh_user": credential.ssh_user,
                    "ssh_password": credential.ssh_password
                }
            return None

        except Exception as e:
            logger.error(f"获取SSH凭据失败 {server_ip}: {e}")
            return None
