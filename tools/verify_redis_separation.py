#!/usr/bin/env python3
"""Redis数据库分离验证脚本

验证Redis数据库分离配置是否正确工作。
"""

import asyncio
from utils.redis_client import RedisClient, CredentialRedisClient
from services.credential_service import CredentialService
from utils.task_manager import TaskManager
from models.credential_models import CredentialCreateRequest
from core.logging import get_logger, setup_logging

# 设置日志
setup_logging()
logger = get_logger(__name__)


async def test_redis_separation():
    """测试Redis数据库分离"""
    
    print("🔍 Redis数据库分离验证测试")
    print("="*60)
    
    # 初始化客户端
    task_redis = RedisClient()
    credential_redis = CredentialRedisClient()
    
    try:
        # 连接测试
        print("\n📡 步骤1: 连接测试")
        await task_redis.connect()
        await credential_redis.connect()
        print("✅ 任务Redis连接成功 (DB0)")
        print("✅ 凭据Redis连接成功 (DB1)")
        
        # 初始化服务
        task_manager = TaskManager(task_redis)
        credential_service = CredentialService(credential_redis)
        
        # 测试数据隔离
        print("\n🔐 步骤2: 数据隔离测试")
        
        # 在任务数据库中写入测试数据
        test_task_key = "test_task:separation_test"
        test_task_data = {"test": "task_data", "db": "0"}
        
        success = await task_redis.set_task(test_task_key, test_task_data)
        if success:
            print("✅ 任务数据写入DB0成功")
        else:
            print("❌ 任务数据写入DB0失败")
            return
        
        # 在凭据数据库中写入测试凭据
        test_credential = CredentialCreateRequest(
            server_ip="*************",
            ssh_user="test_user",
            ssh_password="test_password",
            description="分离测试凭据"
        )
        
        try:
            await credential_service.create_credential(test_credential)
            print("✅ 凭据数据写入DB1成功")
        except Exception as e:
            print(f"❌ 凭据数据写入DB1失败: {e}")
            return
        
        # 验证数据隔离
        print("\n🔍 步骤3: 数据隔离验证")
        
        # 检查任务数据是否只在DB0中
        task_exists_in_db0 = await task_redis.exists(test_task_key)
        task_exists_in_db1 = await credential_redis.exists(test_task_key)
        
        print(f"任务数据在DB0中: {task_exists_in_db0}")
        print(f"任务数据在DB1中: {task_exists_in_db1}")
        
        if task_exists_in_db0 and not task_exists_in_db1:
            print("✅ 任务数据隔离正确")
        else:
            print("❌ 任务数据隔离失败")
        
        # 检查凭据数据是否只在DB1中
        credential_key = f"server_credential:*************"
        cred_exists_in_db0 = await task_redis.exists(credential_key)
        cred_exists_in_db1 = await credential_redis.exists(credential_key)
        
        print(f"凭据数据在DB0中: {cred_exists_in_db0}")
        print(f"凭据数据在DB1中: {cred_exists_in_db1}")
        
        if not cred_exists_in_db0 and cred_exists_in_db1:
            print("✅ 凭据数据隔离正确")
        else:
            print("❌ 凭据数据隔离失败")
        
        # 测试服务功能
        print("\n⚙️ 步骤4: 服务功能测试")
        
        # 测试凭据服务
        retrieved_credential = await credential_service.get_credential("*************")
        if retrieved_credential:
            print("✅ 凭据服务读取功能正常")
        else:
            print("❌ 凭据服务读取功能异常")
        
        # 测试凭据列表
        credential_list = await credential_service.list_credentials()
        if credential_list.total > 0:
            print(f"✅ 凭据列表功能正常 (共{credential_list.total}条)")
        else:
            print("❌ 凭据列表功能异常")
        
        # 清理测试数据
        print("\n🧹 步骤5: 清理测试数据")
        
        # 删除测试任务数据
        await task_redis.delete(test_task_key)
        print("✅ 测试任务数据已清理")
        
        # 删除测试凭据
        await credential_service.delete_credential("*************")
        print("✅ 测试凭据已清理")
        
        print("\n🎉 Redis数据库分离验证完成！")
        print("📋 验证结果:")
        print("  - ✅ 数据库连接正常")
        print("  - ✅ 数据隔离正确")
        print("  - ✅ 服务功能正常")
        
    except Exception as e:
        logger.error(f"验证过程中发生错误: {e}")
        print(f"❌ 验证失败: {e}")
        
    finally:
        # 关闭连接
        await task_redis.disconnect()
        await credential_redis.disconnect()
        print("\n🔌 Redis连接已关闭")


async def check_database_status():
    """检查数据库状态"""
    
    print("📊 Redis数据库状态检查")
    print("="*60)
    
    task_redis = RedisClient()
    credential_redis = CredentialRedisClient()
    
    try:
        await task_redis.connect()
        await credential_redis.connect()
        
        # 检查任务数据
        task_patterns = ["build_task:*", "test_task:*", "build_tasks", "test_tasks"]
        print("\n📋 任务数据库 (DB0) 状态:")
        
        for pattern in task_patterns:
            if pattern.endswith("_tasks"):
                # 集合类型
                members = await task_redis.get_set_members(pattern)
                print(f"  {pattern}: {len(members)} 个成员")
            else:
                # 键模式
                keys = await task_redis.scan_keys(pattern)
                print(f"  {pattern}: {len(keys)} 个键")
        
        # 检查凭据数据
        print("\n🔐 凭据数据库 (DB1) 状态:")
        credential_keys = await credential_redis.scan_keys("server_credential:*")
        print(f"  server_credential:*: {len(credential_keys)} 个键")
        
        # 检查是否有数据混合
        print("\n🔍 数据混合检查:")
        
        # 检查DB0中是否有凭据数据
        cred_in_db0 = await task_redis.scan_keys("server_credential:*")
        if cred_in_db0:
            print(f"  ⚠️  警告: DB0中发现 {len(cred_in_db0)} 个凭据键")
        else:
            print("  ✅ DB0中没有凭据数据")
        
        # 检查DB1中是否有任务数据
        tasks_in_db1 = []
        for pattern in task_patterns:
            keys = await credential_redis.scan_keys(pattern)
            tasks_in_db1.extend(keys)
        
        if tasks_in_db1:
            print(f"  ⚠️  警告: DB1中发现 {len(tasks_in_db1)} 个任务键")
        else:
            print("  ✅ DB1中没有任务数据")
        
    except Exception as e:
        logger.error(f"状态检查失败: {e}")
        print(f"❌ 状态检查失败: {e}")
        
    finally:
        await task_redis.disconnect()
        await credential_redis.disconnect()


def main():
    """主函数"""
    print("GWHW网关MCP服务 - Redis数据库分离验证工具")
    print("="*60)
    print("1. 检查数据库状态")
    print("2. 执行分离验证测试")
    print("3. 退出")
    
    while True:
        choice = input("\n请选择操作 (1-3): ").strip()
        
        if choice == "1":
            asyncio.run(check_database_status())
        elif choice == "2":
            asyncio.run(test_redis_separation())
        elif choice == "3":
            print("退出验证工具")
            break
        else:
            print("无效选择，请输入 1-3")


if __name__ == "__main__":
    main()
