[2025-07-24 16:43:28] [INFO] [fastapi_mcp.server] No auth config provided, skipping auth setup
[2025-07-24 16:43:28] [INFO] [fastapi_mcp.server] MCP server listening at /mcp
[2025-07-24 16:43:28] [INFO] [__main__] 启动GWHW网关MCP服务，监听 0.0.0.0:8000
[2025-07-24 16:43:29] [INFO] [fastapi_mcp.server] No auth config provided, skipping auth setup
[2025-07-24 16:43:29] [INFO] [fastapi_mcp.server] MCP server listening at /mcp
[2025-07-24 16:43:29] [INFO] [uvicorn.error] Started server process [82949]
[2025-07-24 16:43:29] [INFO] [uvicorn.error] Waiting for application startup.
[2025-07-24 16:43:29] [INFO] [main] 启动GWHW网关MCP服务
[2025-07-24 16:43:29] [INFO] [utils.redis_client] Redis连接成功: **************:6379
[2025-07-24 16:43:29] [INFO] [services.credential_service] 凭据加密器初始化成功
[2025-07-24 16:43:29] [INFO] [uvicorn.error] Application startup complete.
[2025-07-24 16:43:29] [INFO] [uvicorn.error] Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
[2025-07-24 16:43:41] [INFO] [uvicorn.error] Shutting down
[2025-07-24 16:43:41] [INFO] [uvicorn.error] Waiting for application shutdown.
[2025-07-24 16:43:41] [INFO] [main] 关闭GWHW网关MCP服务
[2025-07-24 16:43:41] [INFO] [utils.redis_client] Redis连接已断开
[2025-07-24 16:43:41] [INFO] [uvicorn.error] Application shutdown complete.
[2025-07-24 16:43:41] [INFO] [uvicorn.error] Finished server process [82949]
