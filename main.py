"""GWHW网关MCP服务主应用

提供GWHW网关的构建、部署和测试功能的MCP服务。
"""

import asyncio
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from fastapi_mcp import FastApiMCP
from api.routes import router
from core.logging import get_logger, setup_logging
from config.settings import settings
from utils.redis_client import RedisClient, CredentialRedisClient
from services.build_service import BuildService
from services.test_service import TestService
from services.credential_service import CredentialService
from utils.task_manager import TaskManager

# 设置日志
setup_logging()
logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    logger.info("启动GWHW网关MCP服务")
    
    # 初始化Redis连接
    redis_client = RedisClient()  # 任务数据库
    await redis_client.connect()

    credential_redis_client = CredentialRedisClient()  # 凭据数据库
    await credential_redis_client.connect()

    # 初始化任务管理器
    task_manager = TaskManager(redis_client)

    # 初始化凭据服务
    credential_service = CredentialService(credential_redis_client)

    # 将服务实例存储到应用状态中
    app.state.redis_client = redis_client
    app.state.credential_redis_client = credential_redis_client
    app.state.task_manager = task_manager
    app.state.credential_service = credential_service
    app.state.build_service = BuildService(task_manager, credential_service)
    app.state.test_service = TestService(task_manager, credential_service)

    yield

    # 关闭时清理
    logger.info("关闭GWHW网关MCP服务")
    await redis_client.disconnect()
    await credential_redis_client.disconnect()


# 创建FastAPI应用
app = FastAPI(
    title="GWHW网关MCP服务",
    description="提供GWHW网关的构建、部署和测试功能",
    version="1.0.0",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含API路由
app.include_router(router, prefix="/api/v1")

# 创建MCP服务器
mcp = FastApiMCP(app)

# 挂载MCP服务器
mcp.mount()


@app.get("/")
async def root():
    """根路径健康检查"""
    return {
        "message": "GWHW网关MCP服务运行中",
        "version": "1.0.0",
        "status": "healthy"
    }


@app.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        # 检查Redis连接
        redis_client = RedisClient()
        await redis_client.ping()
        
        return {
            "status": "healthy",
            "services": {
                "redis": "connected",
                "api": "running"
            }
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }


def main():
    """主函数"""
    logger.info(f"启动GWHW网关MCP服务，监听 {settings.host}:{settings.port}")
    
    # 配置uvicorn日志
    log_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": {
                "format": "[%(asctime)s] [%(levelname)s] [%(name)s] %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S"
            },
        },
        "handlers": {
            "default": {
                "formatter": "default",
                "class": "logging.StreamHandler",
                "stream": "ext://sys.stdout",
            },
            "file": {
                "formatter": "default",
                "class": "logging.FileHandler",
                "filename": settings.log_file,
                "encoding": "utf-8",
            },
            "error_file": {
                "formatter": "default",
                "class": "logging.FileHandler",
                "filename": settings.log_err,
                "encoding": "utf-8",
                "level": "ERROR",
            },
        },
        "loggers": {
            "uvicorn": {
                "handlers": ["default", "file", "error_file"],
                "level": "INFO",
                "propagate": False,
            },
            "uvicorn.error": {
                "handlers": ["default", "file", "error_file"],
                "level": "INFO",
                "propagate": False,
            },
            "uvicorn.access": {
                "handlers": ["default", "file"],
                "level": "INFO",
                "propagate": False,
            },
        },
    }
    
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="info" if not settings.debug else "debug",
        log_config=log_config
    )


if __name__ == "__main__":
    main()
