# GWHW网关MCP服务

GWHW网关MCP（Model Context Protocol）服务是一个基于FastAPI和MCP协议的微服务，提供GWHW网关的构建、部署和测试功能。

## 功能特性

### 🔨 构建与部署
- **功能解耦设计**：构建、编译、部署功能完全解耦，职责单一
- **多服务器支持**：支持编译服务器和部署服务器分离部署
- **远程构建任务**：支持在远程服务器上执行构建脚本
- **插件编译**：支持插件的并行编译（仅编译，不包含部署）
- **hw_inst.zip包部署**：支持将构建产物部署到目标服务器
- **插件部署**：支持插件文件的复制和服务重启
- **统一构建部署**：提供一体化的构建+部署任务
- **统一插件构建部署**：提供一体化的插件编译+部署任务
- **容器化构建**：支持在Docker容器中执行构建任务

### 🌐 网络与认证
- **SSH密码认证**：支持SSH密码和密钥两种认证方式
- **服务器凭据管理**：安全存储和管理服务器SSH凭据，支持自动填充
- **服务器间文件传输**：支持多种文件传输策略（scp、本地中转等）
- **文件完整性验证**：传输后自动验证文件完整性
- **智能传输降级**：传输失败时自动尝试备用方案

### 🧪 测试管理
- **pcap文件测试**：支持本地和上传的pcap文件测试
- **pcap文件回放测试**：支持上传pcap文件并使用tcpreplay进行网络回放测试
- **日志收集**：自动收集测试过程中的各种日志

### 📊 任务管理
- **异步任务执行**：所有任务都在后台异步执行
- **任务状态跟踪**：实时跟踪任务执行状态
- **任务历史记录**：保存任务执行历史和结果
- **智能清理机制**：支持获取结果后自动删除已完成任务，避免不必要的存储占用
- **批量清理功能**：提供手动和自动批量清理已完成任务的功能

### 🏥 系统监控
- **健康检查**：提供服务健康状态检查
- **详细日志记录**：完整的操作日志和错误信息
- **任务执行监控**：实时监控任务执行状态和结果

## 项目架构

### 代码结构
```
gwhw_mcp/
├── api/                    # API接口层
│   ├── __init__.py
│   └── routes.py          # API路由定义（按功能分组）
├── config/                # 配置管理
│   ├── __init__.py
│   └── settings.py        # 应用配置
├── core/                  # 核心功能
│   ├── __init__.py
│   ├── exceptions.py      # 自定义异常
│   └── logging.py         # 日志配置
├── models/                # 数据模型
│   ├── __init__.py
│   ├── request_models.py  # API请求模型（统一管理）
│   ├── response_models.py # 响应模型
│   └── task_models.py     # 任务数据模型
├── services/              # 业务服务层
│   ├── __init__.py
│   ├── build_service.py   # 构建服务（功能解耦）
│   ├── ssh_service.py     # SSH连接服务（支持密码认证）
│   ├── credential_service.py  # 服务器凭据管理服务
│   └── test_service.py    # 测试服务
├── utils/                 # 工具模块
│   ├── __init__.py
│   ├── redis_client.py    # Redis客户端
│   ├── task_manager.py    # 任务管理器
│   └── crypto_utils.py    # 加密工具
├── tests/                 # 测试模块
│   ├── __init__.py
│   ├── test_credential_service.py  # 凭据服务测试
│   └── test_crypto_utils.py        # 加密工具测试
├── main.py               # 主应用入口
├── requirements.txt      # 依赖包列表
└── README.md            # 项目说明文档
```

### 功能解耦架构

#### 设计理念
- **单一职责原则**：每个任务都有明确的单一职责
- **功能解耦**：构建、编译、部署功能完全分离
- **灵活组合**：可以单独使用各功能，也可以组合使用

#### 功能分层
```
┌─────────────────────────────────────────────────────────────┐
│                    API接口层                                │
│  🔨构建与部署  🧪测试管理  📊任务管理  🏥系统监控           │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   业务服务层                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ 构建服务    │  │ 测试服务    │  │ SSH服务     │          │
│  │ - 仅构建    │  │ - pcap测试  │  │ - 密码认证  │          │
│  │ - 仅编译    │  │ - 日志收集  │  │ - 文件传输  │          │
│  │ - 仅部署    │  │ - 回放测试  │  │ - 多种策略  │          │
│  │ - 统一任务  │  └─────────────┘  └─────────────┘          │
│  └─────────────┘                                           │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   基础设施层                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ 任务管理器  │  │ Redis客户端 │  │ 日志系统    │          │
│  │ - 状态跟踪  │  │ - 任务存储  │  │ - 结构化    │          │
│  │ - 生命周期  │  │ - 缓存管理  │  │ - 多级别    │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

### 多服务器部署架构

#### 架构优势
- **灵活部署**：编译和部署可以在不同服务器上执行
- **负载分离**：编译密集型任务和部署任务分离
- **安全隔离**：编译环境和生产环境物理隔离
- **扩展性强**：可以根据需要扩展编译或部署节点

#### 部署模式
```
┌─────────────────┐    文件传输    ┌─────────────────┐
│   编译服务器     │ ──────────────→ │   部署服务器     │
│                │                │                │
│ ┌─────────────┐ │                │ ┌─────────────┐ │
│ │ Docker容器  │ │                │ │ 生产环境    │ │
│ │ - 源码编译  │ │                │ │ - 服务部署  │ │
│ │ - 插件构建  │ │                │ │ - 服务重启  │ │
│ │ - 包生成    │ │                │ │ - 状态监控  │ │
│ └─────────────┘ │                │ └─────────────┘ │
└─────────────────┘                └─────────────────┘
        │                                   │
        └─────────────── MCP服务 ────────────┘
                    ┌─────────────┐
                    │ 任务管理    │
                    │ SSH连接     │
                    │ 文件传输    │
                    │ 状态跟踪    │
                    └─────────────┘
```

### 文件传输机制

#### 传输策略
系统支持多种文件传输策略，确保在不同网络环境下的可靠性：

1. **直接复制**：同服务器内的文件复制
2. **SCP传输**：使用scp命令进行服务器间传输
3. **本地中转**：通过MCP服务所在机器中转传输

#### 传输流程
```
编译服务器                MCP服务                部署服务器
    │                      │                      │
    │ 1. 生成文件           │                      │
    ├─────────────────────→ │                      │
    │                      │ 2. 检测传输策略       │
    │                      ├─────────────────────→ │
    │                      │ 3a. 直接SCP传输       │
    │                      │        或             │
    │ 3b. 下载到本地        │                      │
    ├─────────────────────→ │                      │
    │                      │ 3c. 上传到目标        │
    │                      ├─────────────────────→ │
    │                      │ 4. 验证文件完整性     │
    │                      ├─────────────────────→ │
    │                      │ 5. 确认传输成功       │
    │                      ←─────────────────────┤ │
```

#### 错误处理
- **传输失败自动重试**：支持多种传输方式的自动降级
- **文件完整性验证**：传输后自动验证文件是否存在
- **详细错误日志**：记录传输过程中的详细信息
- **清理机制**：传输失败时自动清理临时文件

## 安装和配置

### 1. 环境要求
- Python 3.8+
- Redis服务器
- SSH访问权限到目标服务器

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 运行测试
```bash
# 运行所有测试
pytest

# 运行凭据管理相关测试
pytest tests/test_credential_service.py tests/test_crypto_utils.py

# 运行测试并显示覆盖率
pytest --cov=services --cov=utils tests/
```

### 3. 生成凭据加密密钥
在配置环境变量之前，需要生成安全的凭据加密密钥：

```bash
# 生成新的加密密钥和配置模板
python -m utils.crypto_utils

# 或者在Python中生成
python -c "from utils.crypto_utils import CryptoUtils; print(CryptoUtils.generate_secure_config_template())"
```

### 4. 环境变量配置
创建`.env`文件或设置环境变量：

```bash
# 服务配置
HOST=0.0.0.0
PORT=8000
DEBUG=false

# Redis配置
REDIS_HOST=**************
REDIS_PORT=6379
REDIS_PASSWORD=qzkj
REDIS_DB=0

# 默认SSH配置
DEFAULT_REMOTE_IP=*************
DEFAULT_SSH_USER=root
DEFAULT_SSH_PASSWORD=root@123        # 默认SSH密码（可选，支持密钥认证）
DEFAULT_CONTAINER_NAME=build

# 默认路径配置
DEFAULT_BUILD_SCRIPT_PATH=/home/<USER>/build
DEFAULT_CONTAINER_PLUGIN_PATH=/home/<USER>/src/hw/gw_parser/parser/mongo_parser
DEFAULT_SERVER_PLUGIN_PATH=/opt/apigw/gwhw/parser/mongo_parser
DEFAULT_MAKE_JOBS=4

# 测试配置
DEFAULT_WAIT_SECONDS=5
DEFAULT_LOG_LINES=100
DEFAULT_ETH_NAME=lo                    # 默认网口名称（用于tcpreplay）
PCAP_TASK_DIR=/opt/pcap/task          # pcap上传测试目录
PCAP_REPLAY_DIR=/tmp                  # pcap回放测试目录
UPLOAD_LOG_FILE=/opt/upload/log.file
HW_LOG_PATH=/opt/apigw/gwhw/logs/hw.log
HW_ERR_PATH=/opt/apigw/gwhw/logs/hw.err

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/gwhw_mcp.log
LOG_ERR=logs/gwhw_mcp.err

# 任务管理配置
AUTO_DELETE_COMPLETED_TASKS=true  # 获取完成任务结果后自动删除
TASK_CLEANUP_INTERVAL=3600        # 定期清理间隔（秒）
MAX_TASK_HISTORY=1000            # 最大任务历史记录数

# 凭据管理配置
CREDENTIAL_ENCRYPTION_KEY=your-fernet-key-here  # 凭据加密密钥（必需）
CREDENTIAL_REDIS_PREFIX=server_credential       # Redis键前缀
CREDENTIAL_EXPIRE_TIME=31536000                 # 凭据过期时间（秒，默认1年）
ENABLE_CREDENTIAL_AUDIT=true                    # 是否启用凭据操作审计
CREDENTIAL_BACKUP_ENABLED=true                  # 是否启用凭据备份

# 日志格式说明
# 所有日志都采用易读的格式：[时间] [级别] [模块] 消息内容
# 示例：[2025-06-09 11:08:03] [INFO] [main] 启动GWHW网关MCP服务
```

## 使用方法

### 1. 启动服务
```bash
source /root/logan/mcp/gwhw_mcp/.venv/bin/activate && python main.py
```

服务将在指定端口启动，默认为8000端口。

### 2. API接口

所有API接口已按功能模块重新分组，在Swagger UI中提供清晰的分组展示：

#### 🔨 构建与部署

**启动构建任务（仅构建）**
```http
POST /api/v1/build
Content-Type: application/json

{
  "remote_ip": "*************",      // 可选，默认使用配置值
  "ssh_user": "root",                // 可选，默认使用配置值
  "container_name": "build"          // 可选，默认使用配置值
}
```

**启动部署任务（仅部署hw_inst.zip包）**
```http
POST /api/v1/deploy
Content-Type: application/json

{
  "remote_ip": "*************",      // 可选，部署目标服务器IP
  "ssh_user": "root",                // 可选，部署服务器SSH用户名
  "container_name": "build"          // 可选，容器名称
}
```

**启动插件编译任务（仅编译）**
```http
POST /api/v1/build-plugin
Content-Type: application/json

{
  "plugin_name": "mongo_parser.so",  // 必需，插件名称
  "remote_ip": "*************",      // 可选，编译服务器IP
  "ssh_user": "root",                // 可选，编译服务器SSH用户名
  "container_name": "build"          // 可选，容器名称
}
```

**启动插件部署任务（仅部署）**
```http
POST /api/v1/deploy-plugin
Content-Type: application/json

{
  "plugin_name": "mongo_parser.so",  // 必需，插件名称
  "remote_ip": "*************",      // 可选，部署服务器IP
  "ssh_user": "root",                // 可选，部署服务器SSH用户名
  "container_name": "build"          // 可选，容器名称
}
```

**🆕 启动构建部署统一任务**
```http
POST /api/v1/build-deploy
Content-Type: application/json

{
  // 编译服务器配置
  "build_remote_ip": "*************",     // 可选，编译服务器IP
  "build_ssh_user": "root",               // 可选，编译服务器SSH用户名
  "build_ssh_password": "password123",    // 可选，编译服务器SSH密码
  "container_name": "build",              // 可选，容器名称

  // 部署服务器配置
  "deploy_remote_ip": "*************",    // 可选，部署服务器IP
  "deploy_ssh_user": "root",              // 可选，部署服务器SSH用户名
  "deploy_ssh_password": "password456"    // 可选，部署服务器SSH密码
}
```

**🆕 启动插件构建部署统一任务**
```http
POST /api/v1/build-deploy-plugin
Content-Type: application/json

{
  // 插件信息
  "plugin_name": "mongo_parser.so",       // 必需，插件名称

  // 编译服务器配置
  "build_remote_ip": "*************",     // 可选，编译服务器IP
  "build_ssh_user": "root",               // 可选，编译服务器SSH用户名
  "build_ssh_password": "password123",    // 可选，编译服务器SSH密码
  "container_name": "build",              // 可选，容器名称

  // 部署服务器配置
  "deploy_remote_ip": "*************",    // 可选，部署服务器IP
  "deploy_ssh_user": "root",              // 可选，部署服务器SSH用户名
  "deploy_ssh_password": "password456"    // 可选，部署服务器SSH密码
}
```

> **功能解耦说明**：
> - **构建任务**：仅执行构建脚本，生成hw_inst.zip包
> - **部署任务**：仅部署hw_inst.zip包到目标服务器并执行安装
> - **插件编译任务**：仅编译插件，不包含部署逻辑
> - **插件部署任务**：仅复制插件文件并重启服务
> - **统一任务**：提供一体化的构建+部署或编译+部署功能
>
> **多服务器支持**：
> - 编译服务器和部署服务器可以是不同的机器
> - 支持SSH密码和密钥两种认证方式
> - 自动处理服务器间文件传输
>
> **路径自动生成**：
> - 容器插件路径：`mongo_parser.so` → `/home/<USER>/src/hw/gw_parser/parser/mongo_parser`
> - 服务器插件路径：`mongo_parser.so` → `/opt/apigw/gwhw/parser/mongo_parser/mongo_parser.so`

#### 🔐 凭据管理

**创建服务器凭据**
```http
POST /api/v1/credentials
Content-Type: application/json

{
  "server_ip": "*************",     // 必需，服务器IP地址
  "ssh_user": "root",               // 必需，SSH用户名
  "ssh_password": "password123",    // 必需，SSH密码
  "description": "生产服务器"        // 可选，服务器描述
}
```

**获取凭据列表**
```http
GET /api/v1/credentials
```

**获取指定服务器凭据**
```http
GET /api/v1/credentials/{server_ip}
```

**更新服务器凭据**
```http
PUT /api/v1/credentials/{server_ip}
Content-Type: application/json

{
  "ssh_user": "newuser",            // 可选，新的SSH用户名
  "ssh_password": "newpassword",    // 可选，新的SSH密码
  "description": "更新后的描述"      // 可选，新的描述
}
```

**删除服务器凭据**
```http
DELETE /api/v1/credentials/{server_ip}
```

**批量导入凭据**
```http
POST /api/v1/credentials/batch-import
Content-Type: application/json

{
  "credentials": [
    {
      "server_ip": "*************",
      "ssh_user": "root",
      "ssh_password": "password1",
      "description": "服务器1"
    },
    {
      "server_ip": "*************",
      "ssh_user": "admin",
      "ssh_password": "password2",
      "description": "服务器2"
    }
  ],
  "overwrite_existing": false       // 可选，是否覆盖已存在的凭据
}
```

**备份所有凭据**
```http
GET /api/v1/credentials/backup
```

**从备份恢复凭据**
```http
POST /api/v1/credentials/restore
Content-Type: application/json

{
  "credentials": [...],             // 备份数据中的凭据列表
  "backup_time": "2024-01-01T00:00:00Z",
  "version": "1.0"
}
```

> **凭据管理说明**：
> - **安全存储**：所有密码都使用AES-256加密存储
> - **自动填充**：SSH服务会自动根据服务器IP获取存储的凭据
> - **审计日志**：所有凭据操作都会记录审计日志
> - **备份恢复**：支持凭据数据的完整备份和恢复
> - **批量管理**：支持批量导入和管理多个服务器凭据
>
> **注意事项**：
> - 凭据API返回的数据不包含明文密码，确保安全性
> - 加密密钥丢失后无法恢复已存储的密码，请妥善保管
> - 建议定期备份凭据数据

#### 🧪 测试管理

**启动pcap文件测试**
```http
POST /api/v1/test/pcap
Content-Type: application/json

{
  "pcap_file_path": "/path/to/test.pcap",
  "remote_ip": "*************",  // 可选，测试服务器IP
  "ssh_user": "root",            // 可选，测试服务器SSH用户名
  "wait_seconds": 10,            // 可选，等待处理时间
  "log_lines": 100               // 可选，日志输出行数
}
```

**启动pcap文件上传测试**
```http
POST /api/v1/test/pcap-upload
Content-Type: multipart/form-data

file: test.pcap (文件上传)
remote_ip: ************* (可选)
ssh_user: root (可选)
wait_seconds: 10 (可选)
log_lines: 100 (可选)
```

**启动pcap文件回放测试**
```http
POST /api/v1/test/pcap-replay
Content-Type: multipart/form-data

file: test.pcap (文件上传)
remote_ip: ************* (可选)
ssh_user: root (可选)
eth_name: lo (可选，默认为lo)
wait_seconds: 10 (可选)
log_lines: 100 (可选)
```

> **pcap回放测试说明**：
> - 上传pcap文件到远程服务器的`/tmp`目录
> - 使用tcpreplay命令回放pcap文件：`tcpreplay -i {网口名} -M 100 {pcap文件}`
> - 等待指定时间进行处理
> - 收集测试日志和tcpreplay输出结果
> - 自动清理本地和远程临时文件
>
> **注意**：pcap回放测试使用`/tmp`目录，与pcap上传测试的`/opt/pcap/task`目录分离，避免冲突


#### 📊 任务管理

**获取任务列表**
```http
GET /api/v1/tasks?status=running&limit=50
```

**获取任务详情**
```http
GET /api/v1/tasks/{task_id}
```

**删除任务**
```http
DELETE /api/v1/tasks/{task_id}
```

**清理已完成的任务**
```http
POST /api/v1/tasks/cleanup
```

#### 🏥 系统监控

**健康检查**
```http
GET /api/v1/health
```

### 3. 使用示例

#### 多服务器部署示例

**场景1：不同服务器的构建和部署**
```bash
# 在编译服务器*************上构建，在部署服务器*************上部署
curl -X POST "http://localhost:8000/api/v1/build-deploy" \
  -H "Content-Type: application/json" \
  -d '{
    "build_remote_ip": "*************",
    "build_ssh_user": "root",
    "build_ssh_password": "build_password",
    "container_name": "build",
    "deploy_remote_ip": "*************",
    "deploy_ssh_user": "deploy_user",
    "deploy_ssh_password": "deploy_password"
  }'
```

**场景2：插件在不同服务器编译和部署**
```bash
# 在编译服务器上编译插件，在部署服务器上部署插件
curl -X POST "http://localhost:8000/api/v1/build-deploy-plugin" \
  -H "Content-Type: application/json" \
  -d '{
    "plugin_name": "mongo_parser.so",
    "build_remote_ip": "*************",
    "build_ssh_user": "root",
    "build_ssh_password": "build_password",
    "deploy_remote_ip": "*************",
    "deploy_ssh_user": "root",
    "deploy_ssh_password": "deploy_password"
  }'
```

**场景3：同服务器的简化部署**
```bash
# 在同一服务器上进行构建和部署（使用默认配置）
curl -X POST "http://localhost:8000/api/v1/build-deploy" \
  -H "Content-Type: application/json" \
  -d '{}'
```

#### 凭据管理使用示例

**场景1：添加新服务器凭据**
```bash
# 添加生产服务器凭据
curl -X POST "http://localhost:8000/api/v1/credentials" \
  -H "Content-Type: application/json" \
  -d '{
    "server_ip": "*************",
    "ssh_user": "root",
    "ssh_password": "secure_password_123",
    "description": "生产环境主服务器"
  }'
```

**场景2：批量导入服务器凭据**
```bash
# 批量导入多个服务器凭据
curl -X POST "http://localhost:8000/api/v1/credentials/batch-import" \
  -H "Content-Type: application/json" \
  -d '{
    "credentials": [
      {
        "server_ip": "*************",
        "ssh_user": "root",
        "ssh_password": "password1",
        "description": "Web服务器"
      },
      {
        "server_ip": "*************",
        "ssh_user": "admin",
        "ssh_password": "password2",
        "description": "数据库服务器"
      }
    ],
    "overwrite_existing": false
  }'
```

**场景3：自动凭据填充的构建任务**
```bash
# 不需要指定SSH用户名和密码，系统会自动从凭据存储中获取
curl -X POST "http://localhost:8000/api/v1/build" \
  -H "Content-Type: application/json" \
  -d '{
    "remote_ip": "*************"
  }'
```

**场景4：备份和恢复凭据**
```bash
# 备份所有凭据
curl -X GET "http://localhost:8000/api/v1/credentials/backup" > credentials_backup.json

# 从备份恢复凭据
curl -X POST "http://localhost:8000/api/v1/credentials/restore" \
  -H "Content-Type: application/json" \
  -d @credentials_backup.json
```

#### SSH认证方式示例

**密码认证**
```bash
curl -X POST "http://localhost:8000/api/v1/build" \
  -H "Content-Type: application/json" \
  -d '{
    "remote_ip": "*************",
    "ssh_user": "root",
    "ssh_password": "your_password"
  }'
```

**密钥认证（默认方式）**
```bash
curl -X POST "http://localhost:8000/api/v1/build" \
  -H "Content-Type: application/json" \
  -d '{
    "remote_ip": "*************",
    "ssh_user": "root"
  }'
```

### 4. MCP工具使用

服务同时提供MCP协议支持，可以通过MCP客户端调用以下工具：

- `build`: 启动构建任务
- `deploy`: 启动部署任务
- `build_plugin`: 启动插件编译任务
- `build_deploy`: 启动构建部署统一任务
- `build_deploy_plugin`: 启动插件构建部署统一任务
- `deploy_plugin`: 启动插件部署任务
- `test_pcap`: 启动pcap文件测试
- `get_tasks`: 获取任务列表
- `get_task`: 获取任务详情

### 5. 响应格式

**任务响应**
```json
{
    "task_id": "uuid",
    "status": "pending|running|success|failed|error",
    "message": "任务状态描述",
    "created_at": "2024-01-01T00:00:00Z",
    "started_at": "2024-01-01T00:00:00Z",
    "completed_at": "2024-01-01T00:00:00Z",
    "result": {}
}
```

**成功响应**
```json
{
    "success": true,
    "message": "操作成功",
    "data": {}
}
```

**任务列表响应**
```json
{
    "tasks": [],
    "total": 0,
    "message": "获取任务列表成功"
}
```

## 任务状态说明

### 状态枚举
- `pending`: 任务已创建，等待执行
- `running`: 任务正在执行中
- `success`: 任务执行成功
- `failed`: 任务执行失败
- `error`: 任务执行出错

### 状态流转
1. **创建阶段**: `pending` - 任务刚创建，等待后台处理
2. **执行阶段**: `running` - 任务开始执行，设置started_at时间
3. **完成阶段**: 
   - `success` - 任务成功完成，设置completed_at时间
   - `failed` - 任务执行失败，设置completed_at时间
   - `error` - 任务执行出错，设置completed_at时间

### 自动清理机制
- 当`AUTO_DELETE_COMPLETED_TASKS=true`时，**成功**的任务在获取结果后会自动删除。
- **失败**和**出错**的任务将被保留，以便进行问题排查。
- 支持手动调用清理API批量删除所有已完成的任务（包括成功、失败和出错）。
- 清理操作只影响状态为`success`、`failed`、`error`的任务。

## 开发指南

### 1. 添加新功能
1. 在`services/`目录下创建新的服务类
2. 在`api/routes.py`中添加相应的API端点
3. 在`main.py`中添加MCP工具定义
4. 更新数据模型和配置

### 2. 错误处理
- 使用`core/exceptions.py`中定义的自定义异常
- 所有异常都会被记录到日志中
- API返回标准的HTTP错误响应

### 3. 日志记录
- 使用`core/logging.py`配置的日志系统
- 支持文件和控制台输出
- 可通过环境变量配置日志级别

### 4. 任务管理
- 所有长时间运行的任务都通过`TaskManager`管理
- 任务状态存储在Redis中
- 支持任务的创建、查询、更新和删除
- 提供通用的`update_task_status`方法用于更新任务状态

### 5. 新增功能说明

#### TaskManager新增方法
- `update_task_status(task_id, status, message, result=None)`: 通用任务状态更新方法
- `delete_task_delayed(task_id, delay=1)`: 延迟删除任务方法

#### API接口改进
- 所有主要API接口（除文件上传外）都改为接收JSON格式数据
- 改进了任务状态管理和自动清理机制
- 重构了代码架构，将请求模型从路由文件移至专门的models目录

## 部署建议

### 1. 生产环境
- 使用Gunicorn或uWSGI作为WSGI服务器
- 配置Nginx作为反向代理
- 使用Redis集群提高可用性
- 配置日志轮转

### 2. 容器化部署
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["python", "main.py"]
```

### 3. 监控和告警
- 使用健康检查端点监控服务状态
- 监控Redis连接状态
- 监控任务执行情况和错误率

## 故障排除

### 1. 常见问题
- **Redis连接失败**：检查Redis服务状态和连接配置
- **SSH连接失败**：检查SSH密钥配置和网络连通性
- **任务执行失败**：查看任务日志和错误信息
- **任务状态更新失败**：检查TaskStatus枚举值是否正确使用

### 2. 日志查看
```bash
# 查看应用日志
tail -f logs/gwhw_mcp.log

# 查看错误日志
tail -f logs/gwhw_mcp.err

# 日志格式示例：
# [2025-06-09 11:08:03] [INFO] [main] 启动GWHW网关MCP服务
# [2025-06-09 11:08:03] [INFO] [uvicorn.access] 127.0.0.1:45252 - "GET /health HTTP/1.1" 200
# [2025-06-09 11:08:03] [ERROR] [utils.redis_client] Redis连接失败: Connection refused

# 通过API获取任务详情中的日志信息
```

### 3. 调试模式
设置`DEBUG=true`启用调试模式，获取更详细的错误信息。

### 4. 最近重构和优化

#### 🆕 API重构和功能解耦（最新）
- **功能完全解耦**：构建、编译、部署功能完全分离，职责单一
- **多服务器支持**：支持编译服务器和部署服务器分离部署
- **SSH密码认证**：新增SSH密码认证支持，兼容密钥认证
- **服务器间文件传输**：实现完整的服务器间文件传输机制
- **统一任务接口**：新增构建部署统一任务和插件构建部署统一任务
- **API接口分组**：按功能模块重新组织API接口展示
- **请求模型优化**：新增统一请求模型，支持多服务器配置

#### 架构优化
- **修复了编译/部署失败时调试信息丢失的问题**：当任务执行失败或出错时，`stdout` 和 `stderr` 会被正确记录到任务结果中，方便问题排查。
- **修复了查询任务时因缺少`TaskStatus`导入而导致的500错误**。
- **修复了查询失败任务后被自动删除的问题**：现在只有成功的任务才会被自动清理。
- **修复了TaskManager缺少update_task_status方法的问题**
- **修正了TaskStatus枚举值使用错误（COMPLETED -> SUCCESS）**
- **改进了API接口，统一使用JSON格式数据**
- **完善了任务状态管理和自动清理机制**
- **优化了部署任务接口，实现插件路径自动生成**

### 5. 部署任务路径自动生成机制
部署任务现在支持根据插件名称自动生成容器和服务器插件路径，简化了API调用：

#### 路径生成规则：
- **容器插件路径**：`{默认容器路径基础目录}/{插件基础名称}`
  - 示例：`mongo_parser.so` → `/home/<USER>/src/hw/gw_parser/parser/mongo_parser`
- **服务器插件路径**：`{默认服务器路径}/{插件名称}`
  - 示例：`mongo_parser.so` → `/opt/apigw/gwhw/parser/mongo_parser/mongo_parser.so`

#### 配置参数：
- `DEFAULT_CONTAINER_PLUGIN_PATH`: 默认容器插件路径模板
- `DEFAULT_SERVER_PLUGIN_PATH`: 默认服务器插件路径基础目录

这种设计使得部署任务调用更加简洁，只需要提供插件名称即可自动推导出正确的路径。

### 6. 代码架构重构
项目进行了重要的代码架构优化，提高了代码的可维护性和组织性：

#### 🆕 功能解耦重构（最新）：
- **服务功能解耦**：构建、编译、部署功能完全分离
  - 构建服务：仅负责执行构建脚本
  - 编译服务：仅负责插件编译
  - 部署服务：仅负责文件部署和服务重启
- **SSH服务增强**：支持密码和密钥两种认证方式
- **文件传输服务**：实现多策略的服务器间文件传输
- **统一任务模式**：提供组合式的一体化任务

#### 模型分层重构：
- **请求模型分离**：将API请求模型从 `api/routes.py` 移至 `models/request_models.py`
- **模型职责明确**：
  - `models/request_models.py` - 专门处理API请求数据验证
  - `models/task_models.py` - 专门处理任务数据结构
  - `models/response_models.py` - 专门处理API响应格式
- **统一导入管理**：通过 `models/__init__.py` 提供统一的模型导入入口

#### 重构带来的好处：
- **更好的代码组织**：模型定义集中管理，避免重复
- **提高可维护性**：清晰的分层结构，便于定位和修改
- **便于复用**：其他模块可以轻松导入所需的模型
- **符合最佳实践**：遵循单一职责原则和分层架构设计
- **灵活部署**：支持多服务器分离部署架构
- **功能组合**：可以灵活组合不同功能满足需求

#### 请求模型详细说明：

**基础功能模型**
```python
# 构建相关（解耦后的单一功能）
BuildRequest          # 构建任务请求（仅构建）
DeployRequest         # 部署任务请求（仅部署hw_inst.zip包）
BuildPluginRequest    # 插件编译请求（仅编译）
DeployPluginRequest   # 插件部署请求（仅部署）

# 测试相关
PcapTestRequest       # PCAP测试请求
PcapUploadRequest     # PCAP上传测试请求
PcapReplayRequest     # PCAP回放测试请求
```

**🆕 统一功能模型**
```python
# 多服务器统一任务模型
BuildDeployRequest    # 构建部署统一请求
{
    # 编译服务器配置
    "build_remote_ip": str,      # 编译服务器IP
    "build_ssh_user": str,       # 编译服务器SSH用户名
    "build_ssh_password": str,   # 编译服务器SSH密码（可选）
    "container_name": str,       # 容器名称

    # 部署服务器配置
    "deploy_remote_ip": str,     # 部署服务器IP
    "deploy_ssh_user": str,      # 部署服务器SSH用户名
    "deploy_ssh_password": str   # 部署服务器SSH密码（可选）
}

BuildDeployPluginRequest  # 插件构建部署统一请求
{
    # 插件信息
    "plugin_name": str,          # 插件名称（必需）

    # 编译服务器配置
    "build_remote_ip": str,      # 编译服务器IP
    "build_ssh_user": str,       # 编译服务器SSH用户名
    "build_ssh_password": str,   # 编译服务器SSH密码（可选）
    "container_name": str,       # 容器名称

    # 部署服务器配置
    "deploy_remote_ip": str,     # 部署服务器IP
    "deploy_ssh_user": str,      # 部署服务器SSH用户名
    "deploy_ssh_password": str   # 部署服务器SSH密码（可选）
}
```

**模型变更说明**
- `DeployRequest`：移除了`plugin_name`字段，专用于hw_inst.zip包部署
- `BuildPluginRequest`：现在只包含`plugin_name`字段，专用于插件编译
- 新增统一模型支持多服务器配置和SSH密码认证

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献指南

欢迎提交Issue和Pull Request来改进项目。请确保：
1. 代码符合PEP 8规范
2. 添加适当的测试用例
3. 更新相关文档 