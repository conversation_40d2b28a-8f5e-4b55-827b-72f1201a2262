#!/usr/bin/env python3
"""凭据管理功能演示脚本

演示如何使用新的服务器凭据管理功能。
"""

import asyncio
import json
from utils.crypto_utils import CryptoUtils


async def demo_credential_management():
    """演示凭据管理功能"""
    
    print("🔐 GWHW网关MCP服务 - 凭据管理功能演示")
    print("=" * 50)
    
    # 1. 生成加密密钥
    print("\n1. 生成安全的加密密钥")
    print("-" * 30)
    
    key = CryptoUtils.generate_fernet_key()
    print(f"生成的加密密钥: {key}")
    print("⚠️  请将此密钥添加到您的 .env 文件中：")
    print(f"CREDENTIAL_ENCRYPTION_KEY={key}")
    
    # 2. 演示密码加密和解密
    print("\n2. 密码加密和解密演示")
    print("-" * 30)
    
    original_password = "MySecurePassword123!"
    print(f"原始密码: {original_password}")
    
    encrypted_password = CryptoUtils.encrypt_password(original_password, key)
    print(f"加密后密码: {encrypted_password}")
    
    decrypted_password = CryptoUtils.decrypt_password(encrypted_password, key)
    print(f"解密后密码: {decrypted_password}")
    print(f"密码匹配: {'✅' if original_password == decrypted_password else '❌'}")
    
    # 3. 生成配置模板
    print("\n3. 生成安全配置模板")
    print("-" * 30)
    
    config_template = CryptoUtils.generate_secure_config_template()
    print("配置模板:")
    print(config_template)
    
    # 4. API使用示例
    print("\n4. API使用示例")
    print("-" * 30)
    
    # 创建凭据的示例
    create_credential_example = {
        "server_ip": "*************",
        "ssh_user": "root",
        "ssh_password": "secure_password_123",
        "description": "生产环境主服务器"
    }
    
    print("创建凭据 API 调用示例:")
    print("curl -X POST 'http://localhost:8000/api/v1/credentials' \\")
    print("  -H 'Content-Type: application/json' \\")
    print(f"  -d '{json.dumps(create_credential_example, indent=2)}'")
    
    # 批量导入示例
    batch_import_example = {
        "credentials": [
            {
                "server_ip": "*************",
                "ssh_user": "root",
                "ssh_password": "password1",
                "description": "Web服务器"
            },
            {
                "server_ip": "*************",
                "ssh_user": "admin",
                "ssh_password": "password2",
                "description": "数据库服务器"
            }
        ],
        "overwrite_existing": False
    }
    
    print("\n批量导入凭据 API 调用示例:")
    print("curl -X POST 'http://localhost:8000/api/v1/credentials/batch-import' \\")
    print("  -H 'Content-Type: application/json' \\")
    print(f"  -d '{json.dumps(batch_import_example, indent=2)}'")
    
    # 5. 自动凭据填充示例
    print("\n5. 自动凭据填充演示")
    print("-" * 30)
    
    print("使用自动凭据填充的构建任务:")
    print("curl -X POST 'http://localhost:8000/api/v1/build' \\")
    print("  -H 'Content-Type: application/json' \\")
    print("  -d '{\"remote_ip\": \"*************\"}'")
    print("\n💡 系统会自动从凭据存储中获取该IP的SSH用户名和密码")
    
    # 6. 安全提醒
    print("\n6. 安全提醒")
    print("-" * 30)
    
    security_tips = [
        "🔑 妥善保管加密密钥，丢失后无法恢复已存储的密码",
        "🚫 不要将加密密钥提交到版本控制系统",
        "💾 定期备份凭据数据",
        "📝 启用审计日志以跟踪凭据操作",
        "🔄 定期更新服务器密码并同步到凭据存储",
        "🛡️ 使用强密码和多因素认证保护服务器"
    ]
    
    for tip in security_tips:
        print(f"  {tip}")
    
    print("\n✅ 凭据管理功能演示完成！")
    print("📖 更多详细信息请参考 README.md 文档")


def main():
    """主函数"""
    try:
        asyncio.run(demo_credential_management())
    except KeyboardInterrupt:
        print("\n\n👋 演示已取消")
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")


if __name__ == "__main__":
    main()
