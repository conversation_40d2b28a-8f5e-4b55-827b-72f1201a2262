"""API请求模型

定义所有API端点的请求数据模型。
"""

from typing import Optional
from pydantic import BaseModel, Field


class BuildRequest(BaseModel):
    """构建请求模型"""
    remote_ip: Optional[str] = Field("", description="远程服务器IP")
    ssh_user: Optional[str] = Field("", description="SSH用户名")
    container_name: Optional[str] = Field("", description="容器名称")


class DeployRequest(BaseModel):
    """部署请求模型"""
    remote_ip: Optional[str] = Field("", description="远程服务器IP")
    ssh_user: Optional[str] = Field("", description="SSH用户名")
    container_name: Optional[str] = Field("", description="容器名称")


class BuildPluginRequest(BaseModel):
    """插件编译请求模型"""
    plugin_name: str = Field("postgre_parser.so", description="插件名称")
    remote_ip: Optional[str] = Field("", description="远程服务器IP")
    ssh_user: Optional[str] = Field("", description="SSH用户名")
    container_name: Optional[str] = Field("", description="容器名称")

class DeployPluginRequest(BaseModel):
    """插件部署请求模型"""
    plugin_name: str = Field("postgre_parser.so", description="插件名称")
    remote_ip: Optional[str] = Field("", description="远程服务器IP")
    ssh_user: Optional[str] = Field("", description="SSH用户名")
    container_name: Optional[str] = Field("", description="容器名称")


class BuildDeployRequest(BaseModel):
    """构建部署统一请求模型"""
    # 编译服务器配置
    build_remote_ip: Optional[str] = Field("", description="编译远程服务器IP")
    build_ssh_user: Optional[str] = Field("", description="编译服务器SSH用户名")
    build_ssh_password: Optional[str] = Field("", description="编译服务器SSH密码")
    container_name: Optional[str] = Field("", description="容器名称")

    # 部署服务器配置
    deploy_remote_ip: Optional[str] = Field("", description="部署远程服务器IP")
    deploy_ssh_user: Optional[str] = Field("", description="部署服务器SSH用户名")
    deploy_ssh_password: Optional[str] = Field("", description="部署服务器SSH密码")


class BuildDeployPluginRequest(BaseModel):
    """插件构建部署统一请求模型"""
    # 插件信息
    plugin_name: str = Field("postgre_parser.so", description="插件名称")

    # 编译服务器配置
    build_remote_ip: Optional[str] = Field("", description="编译远程服务器IP")
    build_ssh_user: Optional[str] = Field("", description="编译服务器SSH用户名")
    build_ssh_password: Optional[str] = Field("", description="编译服务器SSH密码")
    container_name: Optional[str] = Field("", description="容器名称")

    # 部署服务器配置
    deploy_remote_ip: Optional[str] = Field("", description="部署远程服务器IP")
    deploy_ssh_user: Optional[str] = Field("", description="部署服务器SSH用户名")
    deploy_ssh_password: Optional[str] = Field("", description="部署服务器SSH密码")


class PcapTestRequest(BaseModel):
    """PCAP测试请求模型"""
    pcap_file_path: str = Field(..., description="pcap文件路径")
    remote_ip: Optional[str] = Field("", description="远程服务器IP")
    ssh_user: Optional[str] = Field("", description="SSH用户名")
    wait_seconds: Optional[int] = Field(None, description="等待处理时间（秒）")
    log_lines: Optional[int] = Field(None, description="日志输出行数")


class PcapUploadRequest(BaseModel):
    """PCAP上传测试请求模型（用于表单数据验证）"""
    remote_ip: Optional[str] = Field("", description="远程服务器IP")
    ssh_user: Optional[str] = Field("", description="SSH用户名")
    wait_seconds: Optional[int] = Field(None, description="等待处理时间（秒）")
    log_lines: Optional[int] = Field(None, description="日志输出行数")


class PcapReplayRequest(BaseModel):
    """PCAP回放测试请求模型（用于表单数据验证）"""
    remote_ip: Optional[str] = Field("", description="远程服务器IP")
    ssh_user: Optional[str] = Field("", description="SSH用户名")
    eth_name: Optional[str] = Field("lo", description="网口名称")
    wait_seconds: Optional[int] = Field(None, description="等待处理时间（秒）")
    log_lines: Optional[int] = Field(None, description="日志输出行数")